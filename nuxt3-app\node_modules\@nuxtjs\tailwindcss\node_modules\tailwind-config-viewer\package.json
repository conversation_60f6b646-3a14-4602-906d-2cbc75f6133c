{"name": "tailwind-config-viewer", "version": "2.0.4", "description": "View your Tailwind CSS config file...visually!", "author": {"name": "<PERSON>", "email": "ryaneo<PERSON><PERSON>@gmail.com"}, "scripts": {"serve": "node cli/index.js -c tailwind.config.sample.js & vue-cli-service serve", "build": "vue-cli-service build", "build:docs": "npm run build && node ./cli export ./docs -c tailwind.config.sample.js", "prepublishOnly": "npm run lint --no-fix && npm run build", "lint": "vue-cli-service lint", "test": "echo \"No test specified\"", "version": "npm run build:docs && git add docs"}, "bin": {"tailwind-config-viewer": "./cli/index.js", "tailwindcss-config-viewer": "./cli/index.js"}, "repository": "github:rogden/tailwind-config-viewer", "bugs": {"url": "https://github.com/rogden/tailwind-config-viewer/issues"}, "keywords": ["tailwind", "tailwindcss"], "license": "MIT", "files": ["server", "cli", "dist", "lib"], "engines": {"node": ">=13"}, "dependencies": {"@koa/router": "^12.0.1", "commander": "^6.0.0", "fs-extra": "^9.0.1", "koa": "^2.14.2", "koa-static": "^5.0.0", "open": "^7.0.4", "portfinder": "^1.0.26", "replace-in-file": "^6.1.0"}, "peerDependencies": {"tailwindcss": "1 || 2 || 2.0.1-compat || 3"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.7.0", "@vue/cli-plugin-eslint": "^3.7.0", "@vue/cli-service": "^3.7.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "core-js": "^2.6.5", "defu": "^3.2.2", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "sass": "^1.26.10", "sass-loader": "^9.0.3", "tailwindcss": "^1.4.4", "tailwindcss-dark-mode": "^1.1.6", "vue": "^2.6.10", "vue-draggable-resizable": "^2.2.0", "vue-intersect": "^1.1.6", "vue-template-compiler": "^2.5.21"}}