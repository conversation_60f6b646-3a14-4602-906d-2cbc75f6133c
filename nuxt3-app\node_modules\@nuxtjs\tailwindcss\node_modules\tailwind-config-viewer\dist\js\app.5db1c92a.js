(function(t){function e(e){for(var n,s,o=e[0],l=e[1],c=e[2],d=0,f=[];d<o.length;d++)s=o[d],Object.prototype.hasOwnProperty.call(i,s)&&i[s]&&f.push(i[s][0]),i[s]=0;for(n in l)Object.prototype.hasOwnProperty.call(l,n)&&(t[n]=l[n]);u&&u(e);while(f.length)f.shift()();return r.push.apply(r,c||[]),a()}function a(){for(var t,e=0;e<r.length;e++){for(var a=r[e],n=!0,o=1;o<a.length;o++){var l=a[o];0!==i[l]&&(n=!1)}n&&(r.splice(e--,1),t=s(s.s=a[0]))}return t}var n={},i={app:0},r=[];function s(e){if(n[e])return n[e].exports;var a=n[e]={i:e,l:!1,exports:{}};return t[e].call(a.exports,a,a.exports,s),a.l=!0,a.exports}s.m=t,s.c=n,s.d=function(t,e,a){s.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:a})},s.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},s.t=function(t,e){if(1&e&&(t=s(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(s.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)s.d(a,n,function(e){return t[e]}.bind(null,n));return a},s.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return s.d(e,"a",e),e},s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},s.p="";var o=window["webpackJsonp"]=window["webpackJsonp"]||[],l=o.push.bind(o);o.push=e,o=o.slice();for(var c=0;c<o.length;c++)e(o[c]);var u=l;r.push([0,"chunk-vendors"]),a()})({0:function(t,e,a){t.exports=a("56d7")},"0a10":function(t,e,a){"use strict";a("fbfb")},"0f43":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"flex flex-wrap -mb-4"},t._l(t.data,(function(a,n){return e("div",{key:n,staticClass:"w-full md:w-36 md:mr-4 mb-4"},[e("div",{staticClass:"mb-2 h-36 bg-gray-200 dark:bg-gray-800 border-gray-500 dark:border-gray-700",style:{borderWidth:a}}),e("CanvasBlockLabel",{attrs:{label:"".concat(t.removeDefaultSuffix("border-".concat(n))),value:a}})],1)})),0)},i=[],r=a("2bcf"),s=a("ed08"),o={components:{CanvasBlockLabel:r["a"]},props:{data:{type:Object,required:!0}},methods:{removeDefaultSuffix:s["c"]}},l=o,c=a("2877"),u=Object(c["a"])(l,n,i,!1,null,null,null);e["default"]=u.exports},"1e22":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"space-y-6"},t._l(t.data,(function(a,n){return e("div",{key:n},[e("CanvasSectionRow",{scopedSlots:t._u([{key:"default",fn:function(t){var n=t.blockClasses;return[e("div",{class:n,style:{maxWidth:a}})]}}],null,!0)}),e("CanvasBlockLabel",{attrs:{label:"max-w-".concat(n),value:a}})],1)})),0)},i=[],r=a("2bcf"),s=a("ad3c"),o={components:{CanvasBlockLabel:r["a"],CanvasSectionRow:s["a"]},props:{data:{type:Object,required:!0}}},l=o,c=a("2877"),u=Object(c["a"])(l,n,i,!1,null,null,null);e["default"]=u.exports},"1fb8":function(t,e,a){"use strict";var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"block z-50 sticky-section-header top-0",class:this.id},[e("div",{staticClass:"sm:inline-block duration-150 p-3 -mt-3 -mx-3 rounded-lg",class:{"stuck shadow-xl bg-white dark:bg-midnight bg-opacity-75 dark:bg-opacity-75":t.stuck}},[t._t("default")],2)])},i=[],r=(a("ac6a"),a("768b")),s=a("75fc"),o=(a("8615"),a("f400"),a("5df3"),a("1c4c"),a("d225")),l=a("b0b4"),c={SENTINEL:"sticky-events--sentinel",SENTINEL_TOP:"sticky-events--sentinel-top",SENTINEL_BOTTOM:"sticky-events--sentinel-bottom"},u=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=e.container,n=void 0===a?document:a,i=e.enabled,r=void 0===i||i,s=e.stickySelector,l=void 0===s?".sticky-events":s;Object(o["a"])(this,t),this.container=n,this.observers=[],this.stickyElements=Array.from(this.container.querySelectorAll(l)),this.stickySelector=l,this.state=new Map,r&&this.enableEvents()}return Object(l["a"])(t,[{key:"setState",value:function(t){this.state.get(t)||this.state.set(t,{isSticky:!1,headerSentinel:this.addSentinel(t,c.SENTINEL_TOP),footerSentinel:this.addSentinel(t,c.SENTINEL_BOTTOM)})}},{key:"enableEvents",value:function(){var t=this;window.self===window.top?(this.observers={header:this.createHeaderObserver(),footer:this.createFooterObserver()},this.stickyElements.forEach((function(e){t.setState(e)}))):console.warn("StickyEvents: There are issues with using IntersectionObservers in an iframe, canceling initialization. Please see https://github.com/w3c/IntersectionObserver/issues/183")}},{key:"disableEvents",value:function(){var t=this,e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e&&this.stickyElements.forEach((function(e){return t.fire(!1,e)})),Object.values(this.observers).forEach((function(t){return t.disconnect()})),this.observers=null,this.state.clear()}},{key:"addStickies",value:function(t){var e,a=this;(e=this.stickyElements).push.apply(e,Object(s["a"])(t)),this.stickyElements.forEach((function(t){return a.setState(t)}))}},{key:"addSticky",value:function(t){this.stickyElements.push(t),this.setState(t)}},{key:"addSentinel",value:function(t,e){var a=document.createElement("div"),n=t.parentElement;switch(t.style.cssText="\n      position: -webkit-sticky;\n      position: sticky;\n    ",a.classList.add(c.SENTINEL,e),Object.assign(a.style,{left:0,position:"absolute",right:0,visibility:"hidden"}),e){case c.SENTINEL_TOP:n.insertBefore(a,t),Object.assign(a.style,this.getSentinelPosition(t,a,e),{position:"relative"}),this.observers.header.observe(a);break;case c.SENTINEL_BOTTOM:n.appendChild(a),Object.assign(a.style,this.getSentinelPosition(t,a,e)),this.observers.footer.observe(a);break}return a}},{key:"createHeaderObserver",value:function(){var e=this;return new IntersectionObserver((function(a){var n=Object(r["a"])(a,1),i=n[0],s=i.boundingClientRect,o=i.isIntersecting,l=i.rootBounds,c=i.target.parentElement,u=c.querySelector(e.stickySelector);c.style.position="relative",s.bottom<l.bottom&&o?e.fire(!1,u,t.POSITION_TOP):s.bottom<=l.top&&!o&&e.fire(!0,u,t.POSITION_TOP)}),Object.assign({threshold:0},!(this.container instanceof HTMLDocument)&&{root:this.container}))}},{key:"createFooterObserver",value:function(){var e=this;return new IntersectionObserver((function(a){var n=Object(r["a"])(a,1),i=n[0],s=i.boundingClientRect,o=i.isIntersecting,l=i.rootBounds,c=i.target.parentElement.querySelector(e.stickySelector);s.top<l.top&&s.bottom<l.bottom&&!o?e.fire(!1,c,t.POSITION_BOTTOM):s.bottom>l.top&&e.isSticking(c)&&o&&e.fire(!0,c,t.POSITION_BOTTOM)}),Object.assign({threshold:1},!(this.container instanceof HTMLDocument)&&{root:this.container}))}},{key:"fire",value:function(e,a,n){a.dispatchEvent(new CustomEvent(t.CHANGE,{detail:{isSticky:e,position:n},bubbles:!0})),a.dispatchEvent(new CustomEvent(e?t.STUCK:t.UNSTUCK,{detail:{isSticky:e,position:n},bubbles:!0})),this.state.set(a,{isSticky:e})}},{key:"getSentinelPosition",value:function(t,e,a){var n=window.getComputedStyle(t),i=window.getComputedStyle(t.parentElement);switch(a){case c.SENTINEL_TOP:return{top:"calc(".concat(n.getPropertyValue("top")," * -1)"),height:1};case c.SENTINEL_BOTTOM:var r=parseInt(i.paddingTop);return{bottom:n.top,height:"".concat(t.getBoundingClientRect().height+r,"px")}}}},{key:"isSticking",value:function(t){var e=t.previousElementSibling,a=t.getBoundingClientRect().top,n=e.getBoundingClientRect().top,i=Math.round(Math.abs(a-n)),r=Math.abs(parseInt(window.getComputedStyle(e).getPropertyValue("top")));return i!==r}}]),t}();u.CHANGE="sticky-change",u.STUCK="sticky-stuck",u.UNSTUCK="sticky-unstuck",u.POSITION_BOTTOM="bottom",u.POSITION_TOP="top";var d={props:{id:{type:String,requied:!0}},data:function(){return{stuck:!1}},mounted:function(){var t=this,e=this.$el.getBoundingClientRect().y,a=new u({stickySelector:".".concat(this.id,".sticky-section-header")});a.stickyElements.forEach((function(a){a.addEventListener(u.CHANGE,(function(a){t.stuck=a.detail&&a.detail.isSticky&&window.scrollY>=e-25}))}))}},f=d,p=(a("eb66"),a("2877")),b=Object(p["a"])(f,n,i,!1,null,"215b9e24",null);e["a"]=b.exports},"21bb":function(t,e,a){"use strict";a("6eb8")},"2a7d":function(t,e,a){"use strict";var n=function(){var t=this,e=t._self._c;return e("button",t._g({staticClass:"py-2 px-4 text-sm text-gray-800 dark:text-gray-400 border border-gray-400 dark:border-gray-700 hover:bg-gray-300 dark-hover:bg-gray-800 focus:outline-none rounded",class:{"bg-gray-300 dark:bg-gray-800":t.selected,"bg-white dark:bg-gray-900":!t.selected}},t.$listeners),[t._t("default")],2)},i=[],r={props:{selected:{type:Boolean}}},s=r,o=a("2877"),l=Object(o["a"])(s,n,i,!1,null,null,null);e["a"]=l.exports},"2b80":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"space-y-6"},t._l(t.data.letterSpacing,(function(a,n){return e("div",{key:n},[e("p",{staticClass:"mb-2 text-2xl leading-none text-gray-900 dark:text-gray-500",style:{letterSpacing:a}},[t._v("\n      "+t._s(t.data.typographyExample)+"\n    ")]),e("CanvasBlockLabel",{attrs:{label:"tracking-".concat(n),value:a}})],1)})),0)},i=[],r=a("2bcf"),s={components:{CanvasBlockLabel:r["a"]},props:{data:{type:Object,required:!0}}},o=s,l=a("2877"),c=Object(l["a"])(o,n,i,!1,null,null,null);e["default"]=c.exports},"2bcf":function(t,e,a){"use strict";var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"relative"},[e("div",{staticClass:"tooltip pointer-events-none absolute z-50 py-1 px-2 text-sm shadow-md bg-gray-800 dark:bg-midnight text-white rounded transition duration-200 overflow-hidden",class:{show:t.showCopyTooltip}},[t._v("\n      "+t._s(t.copyText)+"\n      "),e("br"),t.prefixedClassesToCopy.length>1?e("div",{staticClass:"-mx-2 mt-1 px-2 py-1 border-t border-gray-700 dark:border-gray-800"},t._l(t.prefixedClassesToCopy,(function(a){return e("div",{key:a,staticClass:"text-teal-400"},[t._v("\n          "+t._s(a)+"\n        ")])})),0):t._e()]),e("input",{ref:"label",staticClass:"hidden",attrs:{readonly:""},domProps:{value:t.copyValue}}),e("div",{staticClass:"inline-block text-sm text-gray-800 dark:text-gray-400 font-mono hover:text-teal-600 dark-hover:text-teal-400 cursor-pointer break-words",on:{click:t.copy,mouseover:t.showCopy,mouseout:t.hideCopy}},[t._v("\n    "+t._s(t.prefixClassName(t.label))+"\n  ")]),t.value?e("div",{staticClass:"text-sm text-gray-700 dark:text-gray-500 break-words"},[t._v("\n    "+t._s(t.displayValue)+"\n  ")]):t._e()])},i=[],r=(a("6762"),a("2fdb"),a("ed08")),s=[],o={inject:["prefixClassName","getConfig"],props:{label:{type:String,required:!0},value:{type:String}},data:function(){return{showCopyTooltip:!1,copyText:"Copy",copyClasses:[]}},computed:{copyValue:function(){return this.prefixedClassesToCopy.join(" ")},displayValue:function(){return Object(r["a"])(this.value,this.getConfig())},prefixedClassesToCopy:function(){return this.copyClasses.map(this.prefixClassName)}},methods:{copy:function(t){var e=this;t.shiftKey?!s.includes(this.label)&&s.push(this.label):s=[this.label],this.copyClasses=s,this.$nextTick((function(){e.$refs.label.classList.remove("hidden"),e.$refs.label.select(),e.copyText=s.length>1?"Copied (".concat(s.length,")"):"Copied",document.execCommand("copy"),e.$refs.label.blur(),e.$refs.label.classList.add("hidden"),window.getSelection().removeAllRanges()}))},showCopy:function(){this.copyClasses=[],this.copyText="Copy",this.showCopyTooltip=!0},hideCopy:function(){this.showCopyTooltip=!1}}},l=o,c=(a("5c1f"),a("2877")),u=Object(c["a"])(l,n,i,!1,null,"7c049a40",null);e["a"]=u.exports},"314a":function(t,e,a){},"3b5f":function(t,e,a){},"42de":function(t,e,a){"use strict";var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"relative"},[e("svg",{staticClass:"absolute pointer-events-none",staticStyle:{right:"10px",top:"calc(50% - 6px)"},attrs:{width:"11",height:"11",viewBox:"0 0 9 9",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M4.657 4.243h3.76a.2.2 0 0 1 .141.341l-3.76 3.76a.2.2 0 0 1-.283 0l-3.76-3.76a.2.2 0 0 1 .142-.341h3.76z",fill:"#B8C2CC"}})]),e("select",{directives:[{name:"model",rawName:"v-model",value:t.selected,expression:"selected"}],staticClass:"h-full w-full px-4 py-2 border border-gray-400 dark:border-gray-700 focus:outline-none bg-white dark:bg-gray-900 text-gray-800 dark:text-gray-400 rounded text-sm appearance-none",attrs:{id:"transition-duration"},on:{change:function(e){var a=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.selected=e.target.multiple?a:a[0]}}},t._l(t.options,(function(a,n){return e("option",{key:n,domProps:{value:n}},[t._v(t._s(a))])})),0)])},i=[],r={props:{options:{type:Object,required:!0},value:{type:[Object,String],default:null}},data:function(){return{selected:this.value}},watch:{selected:function(t){this.$emit("input",t)}}},s=r,o=a("2877"),l=Object(o["a"])(s,n,i,!1,null,null,null);e["a"]=l.exports},"47a5":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("StickySectionHeader",{attrs:{id:"section-spacing"}},[e("div",{staticClass:"md:flex"},[e("ButtonGroup",{staticClass:"mb-2 md:mb-0"},[e("Button",{staticClass:"w-full sm:w-32",attrs:{selected:"p"===t.selectedProp},on:{click:function(e){t.selectedProp="p"}}},[t._v("\n          Padding\n        ")]),e("Button",{staticClass:"w-full sm:w-32",attrs:{selected:"m"===t.selectedProp},on:{click:function(e){t.selectedProp="m"}}},[t._v("Margin")]),e("Button",{staticClass:"w-full sm:w-auto",attrs:{selected:"-m"===t.selectedProp},on:{click:function(e){t.selectedProp="-m"}}},[t._v("Negative Margin")])],1),e("Select",{staticClass:"w-full md:w-32 md:ml-2",attrs:{options:t.$options.dimensionOptions},model:{value:t.dimensionProp,callback:function(e){t.dimensionProp=e},expression:"dimensionProp"}})],1)]),e("div",{staticClass:"space-y-6 mt-6"},t._l(t.spacing,(function(a){var n=a.value,i=a.prop;return e("div",{key:i},[e("div",{staticClass:"mb-2 bg-gray-500 dark:bg-gray-700",style:{width:n,height:n}}),e("CanvasBlockLabel",{attrs:{label:"".concat(t.selectedProp).concat(t.dimensionProp?t.dimensionProp:"","-").concat(i),value:n}})],1)})),0)],1)},i=[],r=(a("ac6a"),a("456d"),a("55dd"),a("ed08")),s=a("2bcf"),o=a("69e4"),l=a("2a7d"),c=a("42de"),u=a("1fb8"),d={components:{CanvasBlockLabel:s["a"],ButtonGroup:o["a"],Button:l["a"],Select:c["a"],StickySectionHeader:u["a"]},props:{data:{type:Object,required:!0},config:{type:Object}},data:function(){return{selectedProp:"p",dimensionProp:""}},dimensionOptions:{"":"All",t:"Top",r:"Right",b:"Bottom",l:"Left",x:"Horizontal",y:"Vertical"},computed:{spacing:function(){var t=this;return Object.keys(this.data).sort((function(e,a){var n=-1!==t.data[e].indexOf("rem")?Object(r["b"])(t.data[e],t.config):parseFloat(t.data[e]),i=-1!==t.data[a].indexOf("rem")?Object(r["b"])(t.data[a],t.config):parseFloat(t.data[a]);return n-i})).map((function(e){return{prop:e,value:t.data[e]}}))}}},f=d,p=a("2877"),b=Object(p["a"])(f,n,i,!1,null,null,null);e["default"]=b.exports},5234:function(t,e,a){"use strict";a.r(e);var n=a("768b"),i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"space-y-6"},t._l(t.fontSizes,(function(a){var i=Object(n["a"])(a,2),r=i[0],s=i[1];return e("div",{key:r},[e("p",{staticClass:"mb-2 leading-none text-gray-900 dark:text-gray-500",style:{fontSize:t.getFontSizeValue(s)}},[t._v("\n      "+t._s(t.data.typographyExample)+"\n    ")]),e("CanvasBlockLabel",{attrs:{label:"text-".concat(r),value:t.getFontSizeValue(s)}})],1)})),0)},r=[],s=(a("ac6a"),a("ffc1"),a("55dd"),a("ed08")),o=a("2bcf"),l={components:{CanvasBlockLabel:o["a"]},props:{data:{type:Object,required:!0},config:{type:Object,required:!0}},computed:{fontSizes:function(){var t=this;return Object.entries(this.data.fontSize).sort((function(e,a){return Object(s["b"])(e[1],t.config)>Object(s["b"])(a[1],t.config)?1:Object(s["b"])(e[1],t.config)<Object(s["b"])(a[1],t.config)?-1:0}))}},methods:{getFontSizeValue:function(t){return Array.isArray(t)?t[0]:t}}},c=l,u=a("2877"),d=Object(u["a"])(c,i,r,!1,null,null,null);e["default"]=d.exports},"56d7":function(t,e,a){"use strict";a.r(e);a("cadf"),a("551c"),a("f751"),a("097d");var n=a("2b0e"),i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"font-sans",attrs:{id:"app"}},[e("div",{staticClass:"flex justify-between items-center bg-white dark:bg-gray-900 border-b dark:border-gray-900 p-4 text-gray-700 dark:text-gray-500 text-xl font-bold"},[t._v("\n    Tailwind Config Viewer\n    "),e("a",{staticClass:"text-gray-500 hover:text-gray-700",attrs:{href:"https://github.com/rogden/tailwind-config-viewer",target:"_blank"}},[e("svg",{staticClass:"fill-current w-6 h-6",attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"}},[e("title",[t._v("GitHub")]),e("path",{attrs:{d:"M10 0a10 10 0 0 0-3.16 19.49c.5.1.68-.22.68-.48l-.01-1.7c-2.78.6-3.37-1.34-3.37-1.34-.46-1.16-1.11-1.47-1.11-1.47-.9-.62.07-.6.07-.6 1 .07 1.53 1.03 1.53 1.03.9 1.52 2.34 1.08 2.91.83.1-.65.35-1.09.63-1.34-2.22-.25-4.55-1.11-4.55-4.94 0-1.1.39-1.99 1.03-2.69a3.6 3.6 0 0 1 .1-2.64s.84-.27 2.75 1.02a9.58 9.58 0 0 1 5 0c1.91-1.3 2.75-1.02 2.75-1.02.55 1.37.2 2.4.1 2.64.64.7 1.03 1.6 1.03 2.69 0 3.84-2.34 4.68-4.57 4.93.36.31.68.92.68 1.85l-.01 2.75c0 .26.18.58.69.48A10 10 0 0 0 10 0"}})])])]),e("Canvas",{attrs:{darkMode:t.darkMode},on:{"toggle-dark-mode":function(e){t.darkMode=e}}})],1)},r=[],s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"bg-gray-100 dark:bg-midnight"},[t.config?t._e():e("div",{staticClass:"flex items-center justify-center",staticStyle:{height:"calc(100vh - 63px)"}},[e("p",{staticClass:"text-gray-600 text-center font-bold"},[t._v("Loading Config...")])]),t.config?[e("div",{staticClass:"pt-8 px-3 flex"},[e("div",{staticClass:"hidden md:block flex-none h-full overflow-y-auto top-0 sticky max-h-screen pt-2"},[e("ToggleSwitch",{staticClass:"mb-3 ml-3",attrs:{name:"dark-mode",value:t.darkMode,label:"Dark Mode"},on:{input:function(e){return t.$emit("toggle-dark-mode",e)}}}),e("div",{staticClass:"ml-3 text-sm text-gray-700 dark:text-gray-500"},[t._v("Tailwind v"+t._s(t.config.tailwindVersion))]),e("nav",{staticClass:"pt-3 pr-20 pb-12 px-3 h-full"},t._l(t.configTransformed,(function(a){return e("a",{key:a.title,staticClass:"relative flex items-center py-2 hover:text-gray-900 dark-hover:text-gray-200 text-base rounded-sm",class:[t.activeSection===a?"text-gray-900 dark:text-gray-200":"text-gray-700 dark:text-gray-500"],attrs:{href:"#".concat(a.title)},on:{click:function(e){return t.setActiveSection(a)}}},[e("div",{staticClass:"absolute rounded-full bg-gray-500 dark:bg-gray-600 transition duration-200",class:[t.activeSection===a?"visible opacity-100":"invisible opacity-0"],style:{width:"5px",height:"5px",left:"-12px"}}),t._v("\n            "+t._s(a.title)+"\n          ")])})),0)],1),e("div",{staticClass:"md:pl-4"},t._l(t.configTransformed,(function(a){return e("CanvasSection",{key:a.title,attrs:{title:a.title,id:a.title}},[e("Intersect",{attrs:{threshold:[0],rootMargin:"-40% 0px -60% 0px"},on:{enter:function(e){return t.setActiveSection(a)},leaave:function(e){return t.setActiveSection(null)}}},[e(t.sectionComponent(a.component),{tag:"component",attrs:{data:a.data,config:t.config}})],1)],1)})),1)])]:t._e()],2)},o=[],l=(a("96cf"),a("3b8d")),c=a("c939"),u=a.n(c),d=a("760e");function f(t){return[{themeKey:"backgroundColor",component:"Colors",title:"Colors",data:{backgroundColor:t.backgroundColor,borderColor:t.borderColor,textColor:t.textColor}},{themeKey:"spacing",component:"Spacing",title:"Spacing",data:t.spacing},{themeKey:"fontSize",component:"FontSizes",title:"Font Sizes",data:{fontSize:t.fontSize,typographyExample:t.configViewer.typographyExample}},{themeKey:"fontFamily",component:"FontFamilies",title:"Font Families",data:{fontFamily:t.fontFamily,typographyExample:t.configViewer.typographyExample}},{themeKey:"fontWeight",component:"FontWeight",title:"Font Weight",data:{fontWeight:t.fontWeight,typographyExample:t.configViewer.typographyExample}},{themeKey:"letterSpacing",component:"LetterSpacing",title:"Letter Spacing",data:{letterSpacing:t.letterSpacing,typographyExample:t.configViewer.typographyExample}},{themeKey:"lineHeight",component:"LineHeight",title:"Line Height",data:{lineHeight:t.lineHeight,typographyExample:t.configViewer.typographyExample}},{themeKey:"screens",component:"Screens",title:"Screens",data:t.screens},{themeKey:"boxShadow",component:"Shadows",title:"Shadows",data:t.boxShadow},{themeKey:"opacity",component:"Opacity",title:"Opacity",data:t.opacity},{themeKey:"borderRadius",component:"BorderRadius",title:"Border Radius",data:t.borderRadius},{themeKey:"borderWidth",component:"BorderWidth",title:"Border Width",data:t.borderWidth},{themeKey:"transitionTimingFunction",component:"Transitions",title:"Transitions",data:{timing:t.transitionTimingFunction,duration:t.transitionDuration,delay:t.transitionDelay}},{themeKey:"minWidth",component:"MinWidth",title:"Min Width",data:t.minWidth},{themeKey:"width",component:"Width",title:"Width",data:t.width},{themeKey:"maxWidth",component:"MaxWidth",title:"Max Width",data:t.maxWidth},{themeKey:"minHeight",component:"MinHeight",title:"Min Height",data:t.minHeight},{themeKey:"height",component:"Height",title:"Height",data:t.height},{themeKey:"maxHeight",component:"MaxHeight",title:"Max Height",data:t.maxHeight}].filter((function(e){var a=e.themeKey;return t[a]}))}a("ac6a");function p(t){var e=t.configViewer.fonts;e&&("string"===typeof e&&(e=[e]),e.forEach((function(t){var e=document.createElement("link");e.rel="stylesheet",e.href=t,document.head.append(e)})))}var b=function(){var t=this,e=t._self._c;return e("section",{staticClass:"mb-12 max-w-full"},[e("h1",{staticClass:"mb-2 text-3xl text-gray-800 dark:text-gray-500"},[t._v(t._s(t.title))]),e("div",{staticClass:"bg-white dark:bg-gray-900 p-6 rounded border-gray-300 dark:border-gray-900 border"},[t._t("default")],2)])},v=[],y={props:{title:{type:String,required:!0}}},h=y,g=a("2877"),m=Object(g["a"])(h,b,v,!1,null,null,null),k=m.exports,C=a("8163"),x={theme:{configViewer:{baseFontSize:16,typographyExample:"The quick brown fox jumps over the lazy dog."}}},w={components:{CanvasSection:k,ToggleSwitch:C["a"],Intersect:d["a"]},provide:function(){return{prefixClassName:this.prefixClassName,getConfig:this.getConfig}},props:{darkMode:{type:Boolean,required:!1}},data:function(){return{activeSection:null,config:null,configTransformed:null}},methods:{sectionComponent:function(t){return a("c79b")("./".concat(t,".vue")).default},prefixClassName:function(t){return this.config.prefix?"".concat(this.config.prefix).concat(t):t},getConfig:function(){return this.config},setActiveSection:function(t){this.activeSection=t}},mounted:function(){var t=Object(l["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,fetch(window.__TCV_CONFIG.configPath);case 2:return e=t.sent,t.next=5,e.json();case 5:this.config=t.sent,this.config=u()(this.config,x),this.configTransformed=f(this.config.theme),p(this.config.theme);case 9:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()},S=w,_=Object(g["a"])(S,s,o,!1,null,null,null),O=_.exports,j=O,B={components:{Canvas:j},data:function(){return{darkMode:!1}},watch:{darkMode:function(t){document.querySelector("body").classList.toggle("mode-dark",t),localStorage.setItem("tcvDarkMode",t)}},mounted:function(){this.darkMode="true"===localStorage.getItem("tcvDarkMode")}},T=B,E=(a("21bb"),Object(g["a"])(T,i,r,!1,null,null,null)),P=E.exports;a("a2f0");n["a"].config.productionTip=!1,new n["a"]({render:function(t){return t(P)}}).$mount("#app")},"5c1f":function(t,e,a){"use strict";a("d706")},6812:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"space-y-6"},t._l(t.data,(function(a,n){return e("div",{key:n},[e("CanvasSectionRow",{style:{height:a},scopedSlots:t._u([{key:"default",fn:function(t){var n=t.blockClasses;return[e("div",{class:n,style:{height:a}})]}}],null,!0)}),e("CanvasBlockLabel",{attrs:{label:"max-h-".concat(n),value:a}})],1)})),0)},i=[],r=a("2bcf"),s=a("ad3c"),o={components:{CanvasBlockLabel:r["a"],CanvasSectionRow:s["a"]},props:{data:{type:Object,required:!0}}},l=o,c=a("2877"),u=Object(c["a"])(l,n,i,!1,null,null,null);e["default"]=u.exports},"68f4":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"space-y-6"},t._l(t.data.lineHeight,(function(a,n){return e("div",{key:n},[e("div",{staticClass:"text-lg mb-2 text-gray-900 dark:text-gray-500",style:{lineHeight:a}},[e("p",[t._v(t._s(t.data.typographyExample))]),e("p",[t._v(t._s(t.data.typographyExample))])]),e("CanvasBlockLabel",{attrs:{label:"leading-".concat(n),value:a}})],1)})),0)},i=[],r=a("2bcf"),s={components:{CanvasBlockLabel:r["a"]},props:{data:{type:Object,required:!0}}},o=s,l=(a("d248"),a("2877")),c=Object(l["a"])(o,n,i,!1,null,"3558d63b",null);e["default"]=c.exports},"69e4":function(t,e,a){"use strict";var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"button-group flex"},[t._t("default")],2)},i=[],r=(a("0a10"),a("2877")),s={},o=Object(r["a"])(s,n,i,!1,null,null,null);e["a"]=o.exports},"6eb8":function(t,e,a){},"70bf":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"space-y-6"},t._l(t.data.fontWeight,(function(a,n){return e("div",{key:n},[e("p",{staticClass:"mb-2 leading-none text-2xl text-gray-900 dark:text-gray-500",style:{fontWeight:a}},[t._v("\n      "+t._s(t.data.typographyExample)+"\n    ")]),e("CanvasBlockLabel",{attrs:{label:"font-".concat(n),value:String(a)}})],1)})),0)},i=[],r=a("2bcf"),s={components:{CanvasBlockLabel:r["a"]},props:{data:{type:Object,required:!0}}},o=s,l=a("2877"),c=Object(l["a"])(o,n,i,!1,null,null,null);e["default"]=c.exports},8163:function(t,e,a){"use strict";a("7f7f");var n=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.checked,expression:"checked"}],staticClass:"toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer",attrs:{type:"checkbox",name:t.name,id:t.name},domProps:{checked:Array.isArray(t.checked)?t._i(t.checked,null)>-1:t.checked},on:{change:function(e){var a=t.checked,n=e.target,i=!!n.checked;if(Array.isArray(a)){var r=null,s=t._i(a,r);n.checked?s<0&&(t.checked=a.concat([r])):s>-1&&(t.checked=a.slice(0,s).concat(a.slice(s+1)))}else t.checked=i}}}),e("label",{staticClass:"toggle-label block overflow-hidden h-6 rounded-full cursor-pointer",attrs:{for:t.name}})]),t.label?e("label",{staticClass:"text-xs text-gray-700 dark:text-gray-500",attrs:{for:t.name}},[t._v(t._s(t.label))]):t._e()])},i=[],r={props:{name:{type:String,required:!0},value:{type:Boolean,default:!1},label:{type:String,required:!1}},data:function(){return{checked:this.value}},watch:{checked:function(t){this.$emit("input",t)}}},s=r,o=(a("9082"),a("2877")),l=Object(o["a"])(s,n,i,!1,null,null,null);e["a"]=l.exports},"87b8":function(t,e,a){"use strict";a.r(e);a("6762"),a("2fdb");var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"space-y-6"},[t._l(t.fixedWidths,(function(a,n){return e("div",{key:n},[e("CanvasSectionRow",{scopedSlots:t._u([{key:"default",fn:function(t){var n=t.blockClasses;return[e("div",{class:n,style:{width:a.includes("vw")?"100%":a,maxWidth:"100%"}})]}}],null,!0)}),e("CanvasBlockLabel",{attrs:{label:"w-".concat(n),value:a}})],1)})),t._l(t.percentWidths,(function(a,n){return e("div",{key:n,staticClass:"mb-6"},[e("CanvasSectionRow",{scopedSlots:t._u([{key:"default",fn:function(t){var n=t.blockClasses;return[e("div",{class:n,style:{width:a,maxWidth:"100%"}})]}}],null,!0)}),e("CanvasBlockLabel",{attrs:{label:"w-".concat(n),value:a}})],1)}))],2)},i=[],r=(a("8e6e"),a("bd86")),s=(a("ac6a"),a("456d"),a("2bcf")),o=a("ad3c");function l(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function c(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?l(Object(a),!0).forEach((function(e){Object(r["a"])(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}var u={components:{CanvasBlockLabel:s["a"],CanvasSectionRow:o["a"]},props:{data:{type:Object,required:!0}},computed:{percentWidths:function(){var t=this;return Object.keys(this.data).filter((function(e){return-1!==t.data[e].indexOf("%")})).reduce((function(e,a){return c(c({},e),{},Object(r["a"])({},a,t.data[a]))}),{})},fixedWidths:function(){var t=this;return Object.keys(this.data).filter((function(e){return-1===t.data[e].indexOf("%")})).reduce((function(e,a){return c(c({},e),{},Object(r["a"])({},a,t.data[a]))}),{})}}},d=u,f=a("2877"),p=Object(f["a"])(d,n,i,!1,null,null,null);e["default"]=p.exports},9082:function(t,e,a){"use strict";a("ceac")},9907:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"flex flex-wrap -mb-4"},t._l(t.data,(function(a,n){return e("div",{key:n,staticClass:"w-full md:w-36 md:mr-4 mb-4"},[e("div",{staticClass:"bg-gray-500 dark:bg-gray-700 mb-2 md:w-36 h-36",style:{borderRadius:a}}),e("CanvasBlockLabel",{attrs:{label:"".concat(t.removeDefaultSuffix("rounded-".concat(n))),value:a}})],1)})),0)},i=[],r=a("2bcf"),s=a("ed08"),o={components:{CanvasBlockLabel:r["a"]},props:{data:{type:Object,required:!0}},methods:{removeDefaultSuffix:s["c"]}},l=o,c=a("2877"),u=Object(c["a"])(l,n,i,!1,null,null,null);e["default"]=u.exports},a2f0:function(t,e,a){},a36e:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"space-y-6"},t._l(t.data,(function(a,n){return e("div",{key:n},[e("CanvasSectionRow",{attrs:{hasBg:!1},scopedSlots:t._u([{key:"default",fn:function(t){var n=t.blockClasses;return[e("div",{class:n,style:{maxWidth:a}})]}}],null,!0)}),e("CanvasBlockLabel",{attrs:{label:"".concat(n).concat(t.config.separator),value:a}})],1)})),0)},i=[],r=a("2bcf"),s=a("ad3c"),o={components:{CanvasBlockLabel:r["a"],CanvasSectionRow:s["a"]},props:{data:{type:Object,required:!0},config:{type:Object}}},l=o,c=a("2877"),u=Object(c["a"])(l,n,i,!1,null,null,null);e["default"]=u.exports},ad3c:function(t,e,a){"use strict";var n=function(){var t=this,e=t._self._c;return e("div",t._b({staticClass:"mb-2 h-28",class:{"bg-gray-200 dark:bg-gray-800":t.hasBg}},"div",t.$attrs,!1),[t._t("default",null,{blockClasses:t.blockClasses})],2)},i=[],r={props:{hasBg:{type:Boolean,default:!0}},data:function(){return{blockClasses:"bg-gray-500 dark:bg-gray-700 h-28"}}},s=r,o=a("2877"),l=Object(o["a"])(s,n,i,!1,null,null,null);e["a"]=l.exports},b3cb:function(t,e,a){"use strict";a.r(e);var n=a("768b"),i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"space-y-6"},t._l(t.fontFamilies,(function(a){var i=Object(n["a"])(a,2),r=i[0],s=i[1];return e("div",{key:r},[e("p",{staticClass:"mb-2 leading-none text-2xl text-gray-900 dark:text-gray-500",style:{fontFamily:t.getFontFamilyValue(s)}},[t._v("\n      "+t._s(t.data.typographyExample)+"\n    ")]),e("CanvasBlockLabel",{attrs:{label:"font-".concat(r),value:t.getFontFamilyValue(s)}})],1)})),0)},r=[],s=(a("ac6a"),a("ffc1"),a("2bcf")),o={components:{CanvasBlockLabel:s["a"]},props:{data:{type:Object,required:!0},config:{type:Object,required:!0}},computed:{fontFamilies:function(){return Object.entries(this.data.fontFamily)}},methods:{getFontFamilyValue:function(t){return Array.isArray(t)?t.join(", "):t}}},l=o,c=a("2877"),u=Object(c["a"])(l,i,r,!1,null,null,null);e["default"]=u.exports},bd10:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"space-y-6"},t._l(t.data,(function(a,n){return e("div",{key:n},[e("CanvasSectionRow",{scopedSlots:t._u([{key:"default",fn:function(t){var n=t.blockClasses;return[e("div",{class:n,style:{maxWidth:a}})]}}],null,!0)}),e("CanvasBlockLabel",{attrs:{label:"min-w-".concat(n),value:a}})],1)})),0)},i=[],r=a("2bcf"),s=a("ad3c"),o={components:{CanvasBlockLabel:r["a"],CanvasSectionRow:s["a"]},props:{data:{type:Object,required:!0}}},l=o,c=a("2877"),u=Object(c["a"])(l,n,i,!1,null,null,null);e["default"]=u.exports},c79b:function(t,e,a){var n={"./BorderRadius.vue":"9907","./BorderWidth.vue":"0f43","./Colors.vue":"ee61","./FontFamilies.vue":"b3cb","./FontSizes.vue":"5234","./FontWeight.vue":"70bf","./Height.vue":"e211","./LetterSpacing.vue":"2b80","./LineHeight.vue":"68f4","./MaxHeight.vue":"6812","./MaxWidth.vue":"1e22","./MinHeight.vue":"e3f7","./MinWidth.vue":"bd10","./Opacity.vue":"f7f9","./Screens.vue":"a36e","./Shadows.vue":"e9d1","./Spacing.vue":"47a5","./Transitions.vue":"efaf","./Width.vue":"87b8"};function i(t){var e=r(t);return a(e)}function r(t){if(!a.o(n,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return n[t]}i.keys=function(){return Object.keys(n)},i.resolve=r,t.exports=i,i.id="c79b"},cbe9:function(t,e,a){"use strict";a("314a")},ceac:function(t,e,a){},d248:function(t,e,a){"use strict";a("d4e9")},d4e9:function(t,e,a){},d706:function(t,e,a){},e211:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"space-y-6"},t._l(t.data,(function(a,n){return e("div",{key:n},[e("CanvasSectionRow",{style:{height:a},scopedSlots:t._u([{key:"default",fn:function(t){var n=t.blockClasses;return[e("div",{class:n,style:{height:a}})]}}],null,!0)}),e("CanvasBlockLabel",{attrs:{label:"h-".concat(n),value:a}})],1)})),0)},i=[],r=a("2bcf"),s=a("ad3c"),o={components:{CanvasBlockLabel:r["a"],CanvasSectionRow:s["a"]},props:{data:{type:Object,required:!0}}},l=o,c=a("2877"),u=Object(c["a"])(l,n,i,!1,null,null,null);e["default"]=u.exports},e3f7:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"space-y-6"},t._l(t.data,(function(a,n){return e("div",{key:n},[e("CanvasSectionRow",{style:{minHeight:a,height:"auto"},scopedSlots:t._u([{key:"default",fn:function(t){var n=t.blockClasses;return[e("div",{class:n,style:{minHeight:a,height:"auto"}})]}}],null,!0)}),e("CanvasBlockLabel",{attrs:{label:"min-h-".concat(n),value:a}})],1)})),0)},i=[],r=a("2bcf"),s=a("ad3c"),o={components:{CanvasBlockLabel:r["a"],CanvasSectionRow:s["a"]},props:{data:{type:Object,required:!0}}},l=o,c=a("2877"),u=Object(c["a"])(l,n,i,!1,null,null,null);e["default"]=u.exports},e9d1:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"flex flex-wrap -mb-4"},t._l(t.data,(function(a,n){return e("div",{key:n,staticClass:"mb-4 md:mr-8 w-full md:w-36"},[e("div",{staticClass:"mb-2 w-full md:w-36 h-36 dark:bg-gray-700",style:{boxShadow:a}}),e("CanvasBlockLabel",{attrs:{label:"".concat(t.removeDefaultSuffix("shadow-".concat(n))),value:a}})],1)})),0)},i=[],r=a("2bcf"),s=a("ed08"),o={components:{CanvasBlockLabel:r["a"]},props:{data:{type:Object,required:!0}},methods:{removeDefaultSuffix:s["c"]}},l=o,c=a("2877"),u=Object(c["a"])(l,n,i,!1,null,null,null);e["default"]=u.exports},eb66:function(t,e,a){"use strict";a("3b5f")},ed08:function(t,e,a){"use strict";a.d(e,"c",(function(){return n})),a.d(e,"b",(function(){return i})),a.d(e,"a",(function(){return r}));a("386d"),a("a481");var n=function(t){return t.replace(/-(default|DEFAULT)/,"")},i=function(t,e){return"string"===typeof t&&-1===t.search("rem")?parseFloat(t):parseFloat(t)*e.theme.configViewer.baseFontSize},r=function(t,e){return-1===t.search("rem")?t:"".concat(t," (").concat(i(t,e),"px)")}},ee61:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("StickySectionHeader",{attrs:{id:"section-colors"}},[e("ButtonGroup",[e("Button",{staticClass:"w-full sm:w-32",attrs:{selected:"backgroundColor"===t.selectedProp},on:{click:function(e){t.selectedProp="backgroundColor"}}},[t._v("\n        Background\n      ")]),e("Button",{staticClass:"w-full sm:w-32",attrs:{selected:"textColor"===t.selectedProp},on:{click:function(e){t.selectedProp="textColor"}}},[t._v("\n        Text\n      ")]),e("Button",{staticClass:"w-full sm:w-32",attrs:{selected:"borderColor"===t.selectedProp},on:{click:function(e){t.selectedProp="borderColor"}}},[t._v("\n        Border\n      ")])],1)],1),e("div",{staticClass:"flex flex-wrap -mb-4 mt-6"},t._l(t.selectedColorItems,(function(a,n){return e("div",{key:n,staticClass:"w-full md:w-36 mb-4 md:mr-4"},[e("div",{staticClass:"mb-2 flex-none w-full md:w-36 h-16 md:h-36 flex items-center justify-center",class:{"border border-gray-300":"textColor"===t.selectedProp},style:t.tileStyle(a)},["textColor"===t.selectedProp?e("span",{staticClass:"text-3xl",style:{color:a}},[t._v("Aa")]):t._e()]),e("CanvasBlockLabel",{attrs:{label:"".concat(t.selectedPropClassPrefix,"-").concat(n),value:a}})],1)})),0)],1)},i=[],r=a("2bcf"),s=a("69e4"),o=a("2a7d"),l=a("1fb8"),c={components:{CanvasBlockLabel:r["a"],ButtonGroup:s["a"],Button:o["a"],StickySectionHeader:l["a"]},props:{data:{type:Object,required:!0}},data:function(){return{selectedProp:"backgroundColor"}},computed:{selectedColorItems:function(){return this.data[this.selectedProp]},selectedPropClassPrefix:function(){var t={backgroundColor:"bg",textColor:"text",borderColor:"border"};return t[this.selectedProp]}},methods:{tileStyle:function(t){return"backgroundColor"===this.selectedProp?{backgroundColor:t}:"borderColor"===this.selectedProp?{border:"2px solid ".concat(t)}:void 0}}},u=c,d=a("2877"),f=Object(d["a"])(u,n,i,!1,null,null,null);e["default"]=f.exports},efaf:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("StickySectionHeader",{attrs:{id:"section-transitions"}},[e("div",{staticClass:"md:flex items-center"},[e("div",{staticClass:"mb-2 md:mb-0 md:mr-4"},[e("CanvasBlockLabel",{attrs:{label:"duration-".concat(t.selectedDurationKey)}}),e("Select",{staticClass:"w-full mt-2 md:w-32",attrs:{options:t.data.duration},model:{value:t.selectedDurationKey,callback:function(e){t.selectedDurationKey=e},expression:"selectedDurationKey"}})],1),e("div",[e("div",{staticClass:"flex items-center"},[e("CanvasBlockLabel",{attrs:{label:"delay-".concat(t.selectedDelayKey)}})],1),e("Select",{staticClass:"w-full mt-2 md:w-32",attrs:{options:t.data.delay},model:{value:t.selectedDelayKey,callback:function(e){t.selectedDelayKey=e},expression:"selectedDelayKey"}})],1),e("ToggleSwitch",{staticClass:"mt-8 md:ml-4",attrs:{name:"enable-delay",label:"Enable Delay"},model:{value:t.enableDelay,callback:function(e){t.enableDelay=e},expression:"enableDelay"}})],1)]),e("div",{staticClass:"mt-6"},[e("VueDraggableResizable",{attrs:{parent:"",draggable:!1,handles:["mr"],w:"auto",h:"auto","min-width":220}},[e("div",{staticClass:"space-y-6"},t._l(t.data.timing,(function(a,n){return e("div",{key:n},[e("CanvasSectionRow",{staticClass:"transition-container relative",scopedSlots:t._u([{key:"default",fn:function(n){var i=n.blockClasses;return[e("div",{class:["transition-container__block absolute w-28",i],style:{transitionTimingFunction:a,transitionDuration:t.selectedDuration,transitionDelay:t.enableDelay?t.selectedDelay:"0s"}})]}}],null,!0)}),e("div",{staticClass:"sm:flex mb-2 sm:mb-0 sm:divide-x"},[e("CanvasBlockLabel",{attrs:{label:t.removeDefaultSuffix("ease-".concat(n)),value:a}})],1)],1)})),0)])],1)],1)},i=[],r=(a("ac6a"),a("456d"),a("fb19")),s=a.n(r),o=a("2bcf"),l=a("ad3c"),c=a("42de"),u=a("1fb8"),d=a("8163"),f=a("ed08"),p={components:{CanvasBlockLabel:o["a"],CanvasSectionRow:l["a"],VueDraggableResizable:s.a,Select:c["a"],StickySectionHeader:u["a"],ToggleSwitch:d["a"]},props:{data:{type:Object,required:!0}},data:function(){return{selectedDurationKey:Object.keys(this.data.duration)[0],selectedDelayKey:Object.keys(this.data.delay)[0],enableDelay:!1}},computed:{selectedDuration:function(){return this.data.duration[this.selectedDurationKey]},selectedDelay:function(){return this.data.delay[this.selectedDelayKey]}},methods:{removeDefaultSuffix:f["c"]}},b=p,v=(a("cbe9"),a("2877")),y=Object(v["a"])(b,n,i,!1,null,null,null);e["default"]=y.exports},f7f9:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"flex flex-wrap -mb-4"},t._l(t.data,(function(t,a){return e("div",{key:a,staticClass:"w-full md:w-36 md:mr-4 mb-4"},[e("div",{staticClass:"mb-2 bg-gray-500 dark:bg-gray-700 w-full md:w-36 h-36",style:{opacity:t}}),e("CanvasBlockLabel",{attrs:{label:"opacity-".concat(a),value:t}})],1)})),0)},i=[],r=a("2bcf"),s={components:{CanvasBlockLabel:r["a"]},props:{data:{type:Object,required:!0}}},o=s,l=a("2877"),c=Object(l["a"])(o,n,i,!1,null,null,null);e["default"]=c.exports},fbfb:function(t,e,a){}});
//# sourceMappingURL=app.5db1c92a.js.map