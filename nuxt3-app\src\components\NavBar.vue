<template>
  <nav class="bg-white shadow px-6 py-4 flex justify-between items-center">
    <NuxtLink to="/" class="text-lg font-semibold text-blue-700">MyStore</NuxtLink>

    <div class="flex items-center space-x-4">
      <NuxtLink to="/dashboard" class="hover:underline">Dashboard</NuxtLink>
      <button @click="logout" class="text-red-600 hover:underline">Logout</button>
    </div>
  </nav>
</template>

<script setup>
const auth = useAuthStore()
const logout = () => {
  auth.logout()
  navigateTo('/')
}
</script>
