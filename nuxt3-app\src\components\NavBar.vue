<template>
  <nav class="bg-white shadow px-6 py-4 flex justify-between items-center">
    <NuxtLink to="/" class="text-lg font-semibold text-blue-700">MyStore</NuxtLink>

    <div class="flex items-center space-x-4">
      <NuxtLink to="/dashboard" class="hover:underline">Dashboard</NuxtLink>
      <NuxtLink to="/BaseHeader" class="hover:underline">services</NuxtLink>
      <NuxtLink to="/products" class="hover:underline">Products</NuxtLink>
      <NuxtLink to="/about" class="hover:underline">About</NuxtLink>
      <NuxtLink to="/contact" class="hover:underline">Contact</NuxtLink>
      <NuxtLink to="/cart" class="hover:underline">Cart</NuxtLink>
      <button @click="logout" class="text-red-600 hover:underline">Logout</button>
    </div>
  </nav>
</template>

<script setup>
const auth = useAuthStore()
const logout = () => {
  auth.logout()
  navigateTo('/')
}
</script>
