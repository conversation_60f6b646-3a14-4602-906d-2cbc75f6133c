// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 7/16/2025, 3:16:16 PM
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.ts";
const config = [
{"content":{"files":["C:/Users/<USER>/Desktop/understanding nuxtJS/nuxt3-app/src/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/Desktop/understanding nuxtJS/nuxt3-app/src/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/Desktop/understanding nuxtJS/nuxt3-app/src/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/Desktop/understanding nuxtJS/nuxt3-app/src/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/Desktop/understanding nuxtJS/nuxt3-app/src/plugins/**/*.{js,ts,mjs}","C:/Users/<USER>/Desktop/understanding nuxtJS/nuxt3-app/src/composables/**/*.{js,ts,mjs}","C:/Users/<USER>/Desktop/understanding nuxtJS/nuxt3-app/src/utils/**/*.{js,ts,mjs}","C:/Users/<USER>/Desktop/understanding nuxtJS/nuxt3-app/src/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/Desktop/understanding nuxtJS/nuxt3-app/src/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/Desktop/understanding nuxtJS/nuxt3-app/src/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/Desktop/understanding nuxtJS/nuxt3-app/src/app.config.{js,ts,mjs}"]}},
{},
cfg2
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;