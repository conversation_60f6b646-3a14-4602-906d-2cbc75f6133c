<template>
  <div>
    <h1>Welcome to My Nuxt 3 App</h1>
    <p>This is the home page.</p>
    <nav>
      <ul>
        <li><NuxtLink to="/main">Main Page</NuxtLink></li>
        <li><NuxtLink to="/about">About Page</NuxtLink></li>
        <li><NuxtLink to="/products">Products Page</NuxtLink></li>
        <li><NuxtLink to="/products/123">Product Detail (ID: 123)</NuxtLink></li>
      </ul>
    </nav>
  </div> 
  
  <form @submit.prevent="login">
      <h2>Login</h2>
      <input v-model="username" placeholder="Username" />
      <input v-model="password" type="password" />
      <button type="submit">Login</button>
      <p v-if="error">{{ error }}</p>
  </form>
  
</template>

<script setup>
const username = ref('')
const password = ref('')
const error = ref('')

const login = async () => {
  try {
    const res = await $fetch('https://fakestoreapi.com/auth/login', {
      method: 'POST',
      body: { username: username.value, password: password.value }
    })
    useCookie('token').value = res.token
    navigateTo('/dashboard')
  } catch {
    error.value = 'Invalid credentials'
  }
}
</script>

<style scoped>
nav ul {
  list-style: none;
  padding: 0;
}

nav li {
  margin: 10px 0;
}

nav a {
  color: #0066cc;
  text-decoration: none;
  padding: 5px 10px;
  border: 1px solid #0066cc;
  border-radius: 4px;
  display: inline-block;
}

nav a:hover {
  background-color: #0066cc;
  color: white;
}
</style>
