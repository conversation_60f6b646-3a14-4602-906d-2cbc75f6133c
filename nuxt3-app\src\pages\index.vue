<template>
  <!-- <div class="min-h-screen flex justify-center items-center bg-gray-100 p-0 m-0">
    <h1>Welcome to My Nuxt 3 App</h1>
    <br/>
    <p>This is the home page.</p>
  </div>  -->

  <div>
    <BaseHeader />
    <main class="p-6">
      <h2 class="text-h3 mb-4">Available Services</h2>
      <ServicesTable :services="services" />
    </main>
  </div>
  
  <div class="min-h-screen flex justify-center items-center bg-gray-100 mx-auto">
    <form @submit.prevent="login" class="bg-white p-6 rounded shadow w-full max-w-sm space-y-4">
        <h2 class="text-x1 font-semifold">Login</h2>
        <input v-model="username" placeholder="Username" class="w-full border p-2 rounded"/>
        <input v-model="password" type="password" class="w-full border p-2 rounded" />
        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded w-full">Login</button>
        <p v-if="error" class="test-red-600 text-sm">{{ error }}</p>
    </form>
  </div>
</template>

<script setup>
import BaseHeader from '~/components/BaseHeader.vue'
import ServicesTable from '~/components/ServicesTable.vue'

const services = [
  {
    name: 'Haircut',
    description: 'Basic haircut with styling',
    duration: '30 mins',
    price: 'GHS 50',
  },
  {
    name: 'Facial',
    description: 'Deep cleansing facial treatment',
    duration: '45 mins',
    price: 'GHS 80',
  },
]

const username = ref('')
const password = ref('')
const error = ref('')

const login = async () => {
  try {
    const res = await $fetch('https://fakestoreapi.com/auth/login', {
      method: 'POST',
      body: { username: username.value, password: password.value }
    })
    useCookie('token').value = res.token
    navigateTo('/dashboard')
  } catch {
    error.value = 'Invalid credentials'
  }
}


</script>

<style scoped>
nav ul {
  list-style: none;
  padding: 0;
}

nav li {
  margin: 10px 0;
}

nav a {
  color: #0066cc;
  text-decoration: none;
  padding: 5px 10px;
  border: 1px solid #0066cc;
  border-radius: 4px;
  display: inline-block;
}

nav a:hover {
  background-color: #0066cc;
  color: white;
}
</style>
