!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).emnapi={})}(this,function(t){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},e(t,n)};function n(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}"function"==typeof SuppressedError&&SuppressedError;var i=new WeakMap;function r(t){return i.has(t)}var o=function(){function t(t){Object.setPrototypeOf(this,null),i.set(this,t)}return t.prototype=null,t}();function s(t){if(!r(t))throw new TypeError("not external");return i.get(t)}var a=function(){var t;try{t=new Function}catch(t){return!1}return"function"==typeof t}(),u=function(){if("undefined"!=typeof globalThis)return globalThis;var t=function(){return this}();if(!t&&a)try{t=new Function("return this")()}catch(t){}if(!t){if("undefined"==typeof __webpack_public_path__&&"undefined"!=typeof global)return global;if("undefined"!=typeof window)return window;if("undefined"!=typeof self)return self}return t}(),c=function(){function t(){this._exception=void 0,this._caught=!1}return t.prototype.isEmpty=function(){return!this._caught},t.prototype.hasCaught=function(){return this._caught},t.prototype.exception=function(){return this._exception},t.prototype.setError=function(t){this._caught=!0,this._exception=t},t.prototype.reset=function(){this._caught=!1,this._exception=void 0},t.prototype.extractException=function(){var t=this._exception;return this.reset(),t},t}(),p=function(){var t;try{return Boolean(null===(t=Object.getOwnPropertyDescriptor(Function.prototype,"name"))||void 0===t?void 0:t.configurable)}catch(t){return!1}}(),f="object"==typeof Reflect,l="undefined"!=typeof FinalizationRegistry&&"undefined"!=typeof WeakRef,h=function(){try{var t=Symbol();new WeakRef(t),(new WeakMap).set(t,void 0)}catch(t){return!1}return!0}(),d="undefined"!=typeof BigInt;var y=function(){return"undefined"!=typeof __webpack_public_path__||"undefined"!=typeof __webpack_public_path__?"undefined"!=typeof __non_webpack_require__?__non_webpack_require__:void 0:"undefined"!=typeof require?require:void 0}(),_="function"==typeof MessageChannel?MessageChannel:function(){try{return y("worker_threads").MessageChannel}catch(t){}}(),v="function"==typeof setImmediate?setImmediate:function(t){if("function"!=typeof t)throw new TypeError('The "callback" argument must be of type function');if(_){var e=new _;e.port1.onmessage=function(){e.port1.onmessage=null,e=void 0,t()},e.port2.postMessage(null)}else setTimeout(t,0)},g="function"==typeof Buffer?Buffer:function(){try{return y("buffer").Buffer}catch(t){}}(),z="1.4.4",b=2147483647,k=function(){function t(t,e){this.id=t,this.value=e}return t.prototype.data=function(){return s(this.value)},t.prototype.isNumber=function(){return"number"==typeof this.value},t.prototype.isBigInt=function(){return"bigint"==typeof this.value},t.prototype.isString=function(){return"string"==typeof this.value},t.prototype.isFunction=function(){return"function"==typeof this.value},t.prototype.isExternal=function(){return r(this.value)},t.prototype.isObject=function(){return"object"==typeof this.value&&null!==this.value},t.prototype.isArray=function(){return Array.isArray(this.value)},t.prototype.isArrayBuffer=function(){return this.value instanceof ArrayBuffer},t.prototype.isTypedArray=function(){return ArrayBuffer.isView(this.value)&&!(this.value instanceof DataView)},t.prototype.isBuffer=function(t){return!!ArrayBuffer.isView(this.value)||(null!=t||(t=g),"function"==typeof t&&t.isBuffer(this.value))},t.prototype.isDataView=function(){return this.value instanceof DataView},t.prototype.isDate=function(){return this.value instanceof Date},t.prototype.isPromise=function(){return this.value instanceof Promise},t.prototype.isBoolean=function(){return"boolean"==typeof this.value},t.prototype.isUndefined=function(){return void 0===this.value},t.prototype.isSymbol=function(){return"symbol"==typeof this.value},t.prototype.isNull=function(){return null===this.value},t.prototype.dispose=function(){this.value=void 0},t}(),w=function(t){function e(e,n){return t.call(this,e,n)||this}return n(e,t),e.prototype.dispose=function(){},e}(k),S=function(){function t(){this._values=[void 0,t.UNDEFINED,t.NULL,t.FALSE,t.TRUE,t.GLOBAL],this._next=t.MIN_ID}return t.prototype.push=function(t){var e,n=this._next,i=this._values;return n<i.length?(e=i[n]).value=t:(e=new k(n,t),i[n]=e),this._next++,e},t.prototype.erase=function(t,e){this._next=t;for(var n=this._values,i=t;i<e;++i)n[i].dispose()},t.prototype.get=function(t){return this._values[t]},t.prototype.swap=function(t,e){var n=this._values,i=n[t];n[t]=n[e],n[t].id=Number(t),n[e]=i,i.id=Number(e)},t.prototype.dispose=function(){this._values.length=t.MIN_ID,this._next=t.MIN_ID},t.UNDEFINED=new w(1,void 0),t.NULL=new w(2,null),t.FALSE=new w(3,!1),t.TRUE=new w(4,!0),t.GLOBAL=new w(5,u),t.MIN_ID=6,t}(),E=function(){function t(t,e,n,i,r){void 0===r&&(r=i),this.handleStore=t,this.id=e,this.parent=n,this.child=null,null!==n&&(n.child=this),this.start=i,this.end=r,this._escapeCalled=!1,this.callbackInfo={thiz:void 0,data:0,args:void 0,fn:void 0}}return t.prototype.add=function(t){var e=this.handleStore.push(t);return this.end++,e},t.prototype.addExternal=function(t){return this.add(new o(t))},t.prototype.dispose=function(){this._escapeCalled&&(this._escapeCalled=!1),this.start!==this.end&&this.handleStore.erase(this.start,this.end)},t.prototype.escape=function(t){if(this._escapeCalled)return null;if(this._escapeCalled=!0,t<this.start||t>=this.end)return null;this.handleStore.swap(t,this.start);var e=this.handleStore.get(this.start);return this.start++,this.parent.end++,e},t.prototype.escapeCalled=function(){return this._escapeCalled},t}(),m=function(){function t(){this._rootScope=new E(null,0,null,1,S.MIN_ID),this.currentScope=this._rootScope,this._values=[void 0]}return t.prototype.get=function(t){return this._values[t]},t.prototype.openScope=function(t){var e=this.currentScope,n=e.child;if(null!==n)n.start=n.end=e.end;else{var i=e.id+1;n=new E(t.ctx.handleStore,i,e,e.end),this._values[i]=n}return this.currentScope=n,t.openHandleScopes++,n},t.prototype.closeScope=function(t){if(0!==t.openHandleScopes){var e=this.currentScope;this.currentScope=e.parent,e.dispose(),t.openHandleScopes--}},t.prototype.dispose=function(){this.currentScope=this._rootScope,this._values.length=1},t}(),C=function(){function t(){this._next=null,this._prev=null}return t.prototype.dispose=function(){},t.prototype.finalize=function(){},t.prototype.link=function(t){this._prev=t,this._next=t._next,null!==this._next&&(this._next._prev=this),t._next=this},t.prototype.unlink=function(){null!==this._prev&&(this._prev._next=this._next),null!==this._next&&(this._next._prev=this._prev),this._prev=null,this._next=null},t.finalizeAll=function(t){for(;null!==t._next;)t._next.finalize()},t}(),x=function(){function t(t,e,n,i){void 0===e&&(e=0),void 0===n&&(n=0),void 0===i&&(i=0),this.envObject=t,this._finalizeCallback=e,this._finalizeData=n,this._finalizeHint=i,this._makeDynCall_vppp=t.makeDynCall_vppp}return t.prototype.callback=function(){return this._finalizeCallback},t.prototype.data=function(){return this._finalizeData},t.prototype.hint=function(){return this._finalizeHint},t.prototype.resetEnv=function(){this.envObject=void 0},t.prototype.resetFinalizer=function(){this._finalizeCallback=0,this._finalizeData=0,this._finalizeHint=0},t.prototype.callFinalizer=function(){var t=this._finalizeCallback,e=this._finalizeData,n=this._finalizeHint;if(this.resetFinalizer(),t){var i=Number(t);this.envObject?this.envObject.callFinalizer(i,e,n):this._makeDynCall_vppp(i)(0,e,n)}},t.prototype.dispose=function(){this.envObject=void 0,this._makeDynCall_vppp=void 0},t}(),F=function(t){function e(e,n,i,r){var o=t.call(this)||this;return o._finalizer=new x(e,n,i,r),o}return n(e,t),e.create=function(t,n,i,r){var o=new e(t,n,i,r);return o.link(t.finalizing_reflist),o},e.prototype.data=function(){return this._finalizer.data()},e.prototype.dispose=function(){this._finalizer&&(this.unlink(),this._finalizer.envObject.dequeueFinalizer(this),this._finalizer.dispose(),this._finalizer=void 0,t.prototype.dispose.call(this))},e.prototype.finalize=function(){var t;this.unlink();var e=!1;try{this._finalizer.callFinalizer()}catch(n){e=!0,t=n}if(this.dispose(),e)throw t},e}(C);function O(t,e){if(!t.terminatedOrTerminating())throw e}var I=function(){function t(t,e,n,i,r){this.ctx=t,this.moduleApiVersion=e,this.makeDynCall_vppp=n,this.makeDynCall_vp=i,this.abort=r,this.openHandleScopes=0,this.instanceData=null,this.tryCatch=new c,this.refs=1,this.reflist=new C,this.finalizing_reflist=new C,this.pendingFinalizers=[],this.lastError={errorCode:0,engineErrorCode:0,engineReserved:0},this.inGcFinalizer=!1,this._bindingMap=new WeakMap,this.id=0}return t.prototype.canCallIntoJs=function(){return!0},t.prototype.terminatedOrTerminating=function(){return!this.canCallIntoJs()},t.prototype.ref=function(){this.refs++},t.prototype.unref=function(){this.refs--,0===this.refs&&this.dispose()},t.prototype.ensureHandle=function(t){return this.ctx.ensureHandle(t)},t.prototype.ensureHandleId=function(t){return this.ensureHandle(t).id},t.prototype.clearLastError=function(){var t=this.lastError;return 0!==t.errorCode&&(t.errorCode=0),0!==t.engineErrorCode&&(t.engineErrorCode=0),0!==t.engineReserved&&(t.engineReserved=0),0},t.prototype.setLastError=function(t,e,n){void 0===e&&(e=0),void 0===n&&(n=0);var i=this.lastError;return i.errorCode!==t&&(i.errorCode=t),i.engineErrorCode!==e&&(i.engineErrorCode=e),i.engineReserved!==n&&(i.engineReserved=n),t},t.prototype.getReturnStatus=function(){return this.tryCatch.hasCaught()?this.setLastError(10):0},t.prototype.callIntoModule=function(t,e){void 0===e&&(e=O);var n=this.openHandleScopes;this.clearLastError();var i=t(this);(n!==this.openHandleScopes&&this.abort("open_handle_scopes != open_handle_scopes_before"),this.tryCatch.hasCaught())&&e(this,this.tryCatch.extractException());return i},t.prototype.invokeFinalizerFromGC=function(t){if(this.moduleApiVersion!==b)this.enqueueFinalizer(t);else{var e=this.inGcFinalizer;this.inGcFinalizer=!0;try{t.finalize()}finally{this.inGcFinalizer=e}}},t.prototype.checkGCAccess=function(){this.moduleApiVersion===b&&this.inGcFinalizer&&this.abort("Finalizer is calling a function that may affect GC state.\nThe finalizers are run directly from GC and must not affect GC state.\nUse `node_api_post_finalizer` from inside of the finalizer to work around this issue.\nIt schedules the call as a new task in the event loop.")},t.prototype.enqueueFinalizer=function(t){-1===this.pendingFinalizers.indexOf(t)&&this.pendingFinalizers.push(t)},t.prototype.dequeueFinalizer=function(t){var e=this.pendingFinalizers.indexOf(t);-1!==e&&this.pendingFinalizers.splice(e,1)},t.prototype.deleteMe=function(){C.finalizeAll(this.finalizing_reflist),C.finalizeAll(this.reflist),this.tryCatch.extractException(),this.ctx.envStore.remove(this.id)},t.prototype.dispose=function(){0!==this.id&&(this.deleteMe(),this.finalizing_reflist.dispose(),this.reflist.dispose(),this.id=0)},t.prototype.initObjectBinding=function(t){var e={wrapped:0,tag:null};return this._bindingMap.set(t,e),e},t.prototype.getObjectBinding=function(t){return this._bindingMap.has(t)?this._bindingMap.get(t):this.initObjectBinding(t)},t.prototype.setInstanceData=function(t,e,n){this.instanceData&&this.instanceData.dispose(),this.instanceData=F.create(this,e,t,n)},t.prototype.getInstanceData=function(){return this.instanceData?this.instanceData.data():0},t}(),D=function(t){function e(e,n,i,r,o,s,a){var u=t.call(this,e,i,r,o,s)||this;return u.filename=n,u.nodeBinding=a,u.destructing=!1,u.finalizationScheduled=!1,u}return n(e,t),e.prototype.deleteMe=function(){this.destructing=!0,this.drainFinalizerQueue(),t.prototype.deleteMe.call(this)},e.prototype.canCallIntoJs=function(){return t.prototype.canCallIntoJs.call(this)&&this.ctx.canCallIntoJs()},e.prototype.triggerFatalException=function(t){if(this.nodeBinding)this.nodeBinding.napi.fatalException(t);else{if("object"!=typeof process||null===process||"function"!=typeof process._fatalException)throw t;process._fatalException(t)||(console.error(t),process.exit(1))}},e.prototype.callbackIntoModule=function(t,e){return this.callIntoModule(e,function(e,n){if(!e.terminatedOrTerminating()){var i="object"==typeof process&&null!==process,r=!!i&&Boolean(process.execArgv&&-1!==process.execArgv.indexOf("--force-node-api-uncaught-exceptions-policy"));if(e.moduleApiVersion<10&&!r&&!t)(i&&"function"==typeof process.emitWarning?process.emitWarning:function(t,e,n){if(t instanceof Error)console.warn(t.toString());else{var i=n?"[".concat(n,"] "):"";console.warn("".concat(i).concat(e||"Warning",": ").concat(t))}})("Uncaught N-API callback exception detected, please run node with option --force-node-api-uncaught-exceptions-policy=true to handle those exceptions properly.","DeprecationWarning","DEP0168");else e.triggerFatalException(n)}})},e.prototype.callFinalizer=function(t,e,n){this.callFinalizerInternal(1,t,e,n)},e.prototype.callFinalizerInternal=function(t,e,n,i){var r=this.makeDynCall_vppp(e),o=this.id,s=this.ctx.openScope(this);try{this.callbackIntoModule(Boolean(t),function(){r(o,n,i)})}finally{this.ctx.closeScope(this,s)}},e.prototype.enqueueFinalizer=function(e){var n=this;t.prototype.enqueueFinalizer.call(this,e),this.finalizationScheduled||this.destructing||(this.finalizationScheduled=!0,this.ref(),v(function(){n.finalizationScheduled=!1,n.unref(),n.drainFinalizerQueue()}))},e.prototype.drainFinalizerQueue=function(){for(;this.pendingFinalizers.length>0;){this.pendingFinalizers.shift().finalize()}},e}(I);function R(t,e,n,i,r,o,s){(n="number"!=typeof n?8:n)<8?n=8:n>10&&n!==b&&function(t,e){var n="".concat(t," requires Node-API version ").concat(e,", but this version of Node.js only supports version ").concat(10," add-ons.");throw new Error(n)}(e,n);var a=new D(t,e,n,i,r,o,s);return t.envStore.add(a),t.addCleanupHook(a,function(){a.unref()},0),a}var N=function(t){function e(n){var i=this.constructor,r=t.call(this,n)||this,o=i,s=o.prototype;if(!(r instanceof e)){var a=Object.setPrototypeOf;"function"==typeof a?a.call(Object,r,s):r.__proto__=s,"function"==typeof Error.captureStackTrace&&Error.captureStackTrace(r,o)}return r}return n(e,t),e}(Error);Object.defineProperty(N.prototype,"name",{configurable:!0,writable:!0,value:"EmnapiError"});var A=function(t){function e(e,n){return t.call(this,"".concat(e,': The current runtime does not support "FinalizationRegistry" and "WeakRef".').concat(n?" ".concat(n):""))||this}return n(e,t),e}(N);Object.defineProperty(A.prototype,"name",{configurable:!0,writable:!0,value:"NotSupportWeakRefError"});var H=function(t){function e(e,n){return t.call(this,"".concat(e,': The current runtime does not support "Buffer". Consider using buffer polyfill to make sure `globalThis.Buffer` is defined.').concat(n?" ".concat(n):""))||this}return n(e,t),e}(N);Object.defineProperty(H.prototype,"name",{configurable:!0,writable:!0,value:"NotSupportBufferError"});var j,T=function(){function t(t){this._value=t}return t.prototype.deref=function(){return this._value},t.prototype.dispose=function(){this._value=void 0},t}(),B=function(){function t(t){this._ref=new T(t)}return t.prototype.setWeak=function(e,n){if(l&&void 0!==this._ref&&!(this._ref instanceof WeakRef)){var i=this._ref.deref();try{t._registry.register(i,this,this);var r=new WeakRef(i);this._ref.dispose(),this._ref=r,this._param=e,this._callback=n}catch(t){if("symbol"!=typeof i)throw t}}},t.prototype.clearWeak=function(){if(l&&void 0!==this._ref&&this._ref instanceof WeakRef){try{t._registry.unregister(this)}catch(t){}this._param=void 0,this._callback=void 0;var e=this._ref.deref();this._ref=void 0===e?e:new T(e)}},t.prototype.reset=function(){if(l)try{t._registry.unregister(this)}catch(t){}this._param=void 0,this._callback=void 0,this._ref instanceof T&&this._ref.dispose(),this._ref=void 0},t.prototype.isEmpty=function(){return void 0===this._ref},t.prototype.deref=function(){if(void 0!==this._ref)return this._ref.deref()},t._registry=l?new FinalizationRegistry(function(t){t._ref=void 0;var e=t._callback,n=t._param;t._callback=void 0,t._param=void 0,"function"==typeof e&&e(n)}):void 0,t}();t.ReferenceOwnership=void 0,(j=t.ReferenceOwnership||(t.ReferenceOwnership={}))[j.kRuntime=0]="kRuntime",j[j.kUserland=1]="kUserland";var W,M=function(e){function i(t,n,i,r){var o=e.call(this)||this;o.envObject=t,o._refcount=i,o._ownership=r;var s,a=t.ctx.handleStore.get(n);return o.canBeWeak=(s=a).isObject()||s.isFunction()||s.isSymbol(),o.persistent=new B(a.value),o.id=0,0===i&&o._setWeak(),o}return n(i,e),i.weakCallback=function(t){t.persistent.reset(),t.invokeFinalizerFromGC()},i.create=function(t,e,n,r,o,s,a){var u=new i(t,e,n,r);return t.ctx.refStore.add(u),u.link(t.reflist),u},i.prototype.ref=function(){return this.persistent.isEmpty()?0:(1===++this._refcount&&this.canBeWeak&&this.persistent.clearWeak(),this._refcount)},i.prototype.unref=function(){return this.persistent.isEmpty()||0===this._refcount?0:(0===--this._refcount&&this._setWeak(),this._refcount)},i.prototype.get=function(t){if(void 0===t&&(t=this.envObject),this.persistent.isEmpty())return 0;var e=this.persistent.deref();return t.ensureHandle(e).id},i.prototype.resetFinalizer=function(){},i.prototype.data=function(){return 0},i.prototype.refcount=function(){return this._refcount},i.prototype.ownership=function(){return this._ownership},i.prototype.callUserFinalizer=function(){},i.prototype.invokeFinalizerFromGC=function(){this.finalize()},i.prototype._setWeak=function(){this.canBeWeak?this.persistent.setWeak(this,i.weakCallback):this.persistent.reset()},i.prototype.finalize=function(){this.persistent.reset();var e=this._ownership===t.ReferenceOwnership.kRuntime;this.unlink(),this.callUserFinalizer(),e&&this.dispose()},i.prototype.dispose=function(){0!==this.id&&(this.unlink(),this.persistent.reset(),this.envObject.ctx.refStore.remove(this.id),e.prototype.dispose.call(this),this.envObject=void 0,this.id=0)},i}(C),P=function(t){function e(e,n,i,r,o){var s=t.call(this,e,n,i,r)||this;return s._data=o,s}return n(e,t),e.create=function(t,n,i,r,o){var s=new e(t,n,i,r,o);return t.ctx.refStore.add(s),s.link(t.reflist),s},e.prototype.data=function(){return this._data},e}(M),L=function(t){function e(e,n,i,r,o,s,a){var u=t.call(this,e,n,i,r)||this;return u._finalizer=new x(e,o,s,a),u}return n(e,t),e.create=function(t,n,i,r,o,s,a){var u=new e(t,n,i,r,o,s,a);return t.ctx.refStore.add(u),u.link(t.finalizing_reflist),u},e.prototype.resetFinalizer=function(){this._finalizer.resetFinalizer()},e.prototype.data=function(){return this._finalizer.data()},e.prototype.callUserFinalizer=function(){this._finalizer.callFinalizer()},e.prototype.invokeFinalizerFromGC=function(){this._finalizer.envObject.invokeFinalizerFromGC(this)},e.prototype.dispose=function(){this._finalizer&&(this._finalizer.envObject.dequeueFinalizer(this),this._finalizer.dispose(),t.prototype.dispose.call(this),this._finalizer=void 0)},e}(M),U=function(){function t(t,e){this.id=0,this.ctx=t,this.value=e}return t.create=function(e,n){var i=new t(e,n);return e.deferredStore.add(i),i},t.prototype.resolve=function(t){this.value.resolve(t),this.dispose()},t.prototype.reject=function(t){this.value.reject(t),this.dispose()},t.prototype.dispose=function(){this.ctx.deferredStore.remove(this.id),this.id=0,this.value=null,this.ctx=null},t}(),V=function(){function t(){this._values=[void 0],this._values.length=4,this._size=1,this._freeList=[]}return t.prototype.add=function(t){var e;if(this._freeList.length)e=this._freeList.shift();else{e=this._size,this._size++;var n=this._values.length;e>=n&&(this._values.length=n+(n>>1)+16)}t.id=e,this._values[e]=t},t.prototype.get=function(t){return this._values[t]},t.prototype.has=function(t){return void 0!==this._values[t]},t.prototype.remove=function(t){var e=this._values[t];e&&(e.id=0,this._values[t]=void 0,this._freeList.push(Number(t)))},t.prototype.dispose=function(){for(var t=1;t<this._size;++t){var e=this._values[t];null==e||e.dispose()}this._values=[void 0],this._size=1,this._freeList=[]},t}(),G=function(){return function(t,e,n,i){this.envObject=t,this.fn=e,this.arg=n,this.order=i}}(),q=function(){function t(){this._cleanupHooks=[],this._cleanupHookCounter=0}return t.prototype.empty=function(){return 0===this._cleanupHooks.length},t.prototype.add=function(t,e,n){if(this._cleanupHooks.filter(function(i){return i.envObject===t&&i.fn===e&&i.arg===n}).length>0)throw new Error("Can not add same fn and arg twice");this._cleanupHooks.push(new G(t,e,n,this._cleanupHookCounter++))},t.prototype.remove=function(t,e,n){for(var i=0;i<this._cleanupHooks.length;++i){var r=this._cleanupHooks[i];if(r.envObject===t&&r.fn===e&&r.arg===n)return void this._cleanupHooks.splice(i,1)}},t.prototype.drain=function(){var t=this._cleanupHooks.slice();t.sort(function(t,e){return e.order-t.order});for(var e=0;e<t.length;++e){var n=t[e];"number"==typeof n.fn?n.envObject.makeDynCall_vp(n.fn)(n.arg):n.fn(n.arg),this._cleanupHooks.splice(this._cleanupHooks.indexOf(n),1)}},t.prototype.dispose=function(){this._cleanupHooks.length=0,this._cleanupHookCounter=0},t}(),J=function(){function t(){this.refHandle=(new _).port1,this.count=0}return t.prototype.increase=function(){0===this.count&&this.refHandle.ref&&this.refHandle.ref(),this.count++},t.prototype.decrease=function(){0!==this.count&&(1===this.count&&this.refHandle.unref&&this.refHandle.unref(),this.count--)},t}(),Q=function(){function t(){var t=this;this._isStopping=!1,this._canCallIntoJs=!0,this._suppressDestroy=!1,this.envStore=new V,this.scopeStore=new m,this.refStore=new V,this.deferredStore=new V,this.handleStore=new S,this.feature={supportReflect:f,supportFinalizer:l,supportWeakSymbol:h,supportBigInt:d,supportNewFunction:a,canSetFunctionName:p,setImmediate:v,Buffer:g,MessageChannel:_},this.cleanupQueue=new q,"object"==typeof process&&null!==process&&"function"==typeof process.once&&(this.refCounter=new J,process.once("beforeExit",function(){t._suppressDestroy||t.destroy()}))}return t.prototype.suppressDestroy=function(){this._suppressDestroy=!0},t.prototype.getRuntimeVersions=function(){return{version:z,NODE_API_SUPPORTED_VERSION_MAX:10,NAPI_VERSION_EXPERIMENTAL:b,NODE_API_DEFAULT_MODULE_API_VERSION:8}},t.prototype.createNotSupportWeakRefError=function(t,e){return new A(t,e)},t.prototype.createNotSupportBufferError=function(t,e){return new H(t,e)},t.prototype.createReference=function(t,e,n,i){return M.create(t,e,n,i)},t.prototype.createReferenceWithData=function(t,e,n,i,r){return P.create(t,e,n,i,r)},t.prototype.createReferenceWithFinalizer=function(t,e,n,i,r,o,s){return void 0===r&&(r=0),void 0===o&&(o=0),void 0===s&&(s=0),L.create(t,e,n,i,r,o,s)},t.prototype.createDeferred=function(t){return U.create(this,t)},t.prototype.createEnv=function(t,e,n,i,r,o){return R(this,t,e,n,i,r,o)},t.prototype.createTrackedFinalizer=function(t,e,n,i){return F.create(t,e,n,i)},t.prototype.getCurrentScope=function(){return this.scopeStore.currentScope},t.prototype.addToCurrentScope=function(t){return this.scopeStore.currentScope.add(t)},t.prototype.openScope=function(t){return this.scopeStore.openScope(t)},t.prototype.closeScope=function(t,e){this.scopeStore.closeScope(t)},t.prototype.ensureHandle=function(t){switch(t){case void 0:return S.UNDEFINED;case null:return S.NULL;case!0:return S.TRUE;case!1:return S.FALSE;case u:return S.GLOBAL}return this.addToCurrentScope(t)},t.prototype.addCleanupHook=function(t,e,n){this.cleanupQueue.add(t,e,n)},t.prototype.removeCleanupHook=function(t,e,n){this.cleanupQueue.remove(t,e,n)},t.prototype.runCleanup=function(){for(;!this.cleanupQueue.empty();)this.cleanupQueue.drain()},t.prototype.increaseWaitingRequestCounter=function(){var t;null===(t=this.refCounter)||void 0===t||t.increase()},t.prototype.decreaseWaitingRequestCounter=function(){var t;null===(t=this.refCounter)||void 0===t||t.decrease()},t.prototype.setCanCallIntoJs=function(t){this._canCallIntoJs=t},t.prototype.setStopping=function(t){this._isStopping=t},t.prototype.canCallIntoJs=function(){return this._canCallIntoJs&&!this._isStopping},t.prototype.destroy=function(){this.setStopping(!0),this.setCanCallIntoJs(!1),this.runCleanup()},t}();function X(){return new Q}t.ConstHandle=w,t.Context=Q,t.Deferred=U,t.EmnapiError=N,t.Env=I,t.External=o,t.Finalizer=x,t.Handle=k,t.HandleScope=E,t.HandleStore=S,t.NAPI_VERSION_EXPERIMENTAL=b,t.NODE_API_DEFAULT_MODULE_API_VERSION=8,t.NODE_API_SUPPORTED_VERSION_MAX=10,t.NODE_API_SUPPORTED_VERSION_MIN=1,t.NodeEnv=D,t.NotSupportBufferError=H,t.NotSupportWeakRefError=A,t.Persistent=B,t.RefTracker=C,t.Reference=M,t.ReferenceWithData=P,t.ReferenceWithFinalizer=L,t.ScopeStore=m,t.Store=V,t.TrackedFinalizer=F,t.TryCatch=c,t.createContext=X,t.getDefaultContext=function(){return W||(W=X()),W},t.getExternalValue=s,t.isExternal=r,t.isReferenceType=function(t){return"object"==typeof t&&null!==t||"function"==typeof t},t.version=z});
