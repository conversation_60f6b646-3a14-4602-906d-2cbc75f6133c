<template>
  <div class="overflow-x-auto border border-border rounded-lg bg-white dark:bg-card">
    <table class="w-full text-sm text-left">
      <thead class="text-[#111418] bg-white dark:bg-card">
        <tr>
          <TableHead>Name</TableHead>
          <TableHead>Description</TableHead>
          <TableHead>Duration</TableHead>
          <TableHead>Price</TableHead>
          <TableHead>Availability</TableHead>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in services" :key="index" class="border-t border-border">
          <TableData>{{ item.name }}</TableData>
          <TableData class="text-[#60758a]">{{ item.description }}</TableData>
          <TableData class="text-[#60758a]">{{ item.duration }}</TableData>
          <TableData class="text-[#60758a]">{{ item.price }}</TableData>
          <TableData>
            <BaseButton small fullWidth>Available</BaseButton>
          </TableData>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import BaseButton from './BaseButton.vue'
import TableHead from './table/TableHead.vue'
import TableData from './table/TableData.vue'

defineProps({
  services: Array
})
</script>
