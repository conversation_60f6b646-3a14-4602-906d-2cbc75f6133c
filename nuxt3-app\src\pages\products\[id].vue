<template>
  <div class="p-6 max-w-3xl mx-auto">
    <NuxtLink to="/dashboard" class="text-blue-600 hover:underline">&larr; Back to Dashboard</NuxtLink>

    <div v-if="pending" class="text-gray-500 mt-4">Loading product...</div>
    <div v-else-if="error" class="text-red-500 mt-4">Error loading product</div>

    <div v-else class="mt-6">
      <img :src="product.image" alt="" class="w-48 h-48 object-contain mb-4" />
      <h1 class="text-2xl font-bold">{{ product.title }}</h1>
      <p class="text-gray-700 mt-2">{{ product.description }}</p>
      <p class="mt-4 text-lg font-semibold">${{ product.price }}</p>
    </div>
  </div>
</template>

<script setup>
const route = useRoute()
const { data: product, pending, error } = await useFetch(`https://fakestoreapi.com/products/${route.params.id}`)
</script>



