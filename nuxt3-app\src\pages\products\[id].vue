<template>
  <div v-if="pending">Loading...</div>
  <div v-else-if="error">Error: {{ error.message }}</div>
  <div v-else>
    <h2>{{ product.title }}</h2>
    <img :src="product.image" alt="" width="100" />
    <p>{{ product.description }}</p>
    <p><strong>Price: ${{ product.price }}</strong></p>
  </div>
</template>

<script setup>
const route = useRoute()
const { data: product, pending, error } = await useFetch(`https://fakestoreapi.com/products/${route.params.id}`)
</script>



