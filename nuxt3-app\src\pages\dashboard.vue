<template>
  <div class="p-6">
    <h2 class="text-2x1 font-bold mb-4">Welcome to the Dashboard</h2>
    <button @click="logout">Logout</button>

    <div v-if="pending" class="text-gray-500">Loading...</div>
    <div v-else-if="error" class="text-red-500">Failed to load: {{ error.message }}</div>
    

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div v-for="product in products" :key="product.id" class="border rounded p-4">
        <NuxtLink :to="`/products/${product.id}`" class="block hover:underline">
          <img :src="product.image" alt="" class="h-40 w-full object-contain mb-2" />
          <h3 class="font-medium">{{ product.title }}</h3>
          <p class="text-sm text-gray-600">${{ product.price }}</p>
        </NuxtLink>
      </div>
    </div>
    
    <!-- <div>
    <ul>
        <li v-for="product in products" :key="product.id">
            <NuxtLink :to="`/products/${product.id}`">{{ product.title }}</NuxtLink>
        </li>
        </ul>
    </div> -->
  </div>
</template>

<script setup>
const { data: products, pending, error } = await useFetch('https://fakestoreapi.com/products')
const logout = () => {
  useCookie('token').value = null
  navigateTo('/')
}
</script>
