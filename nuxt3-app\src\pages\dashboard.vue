<template>
  <div>
    <h2>Welcome to the Dashboard</h2>
    <button @click="logout">Logout</button>

    <div v-if="pending">Loading...</div>
    <div v-else-if="error">Failed to load: {{ error.message }}</div>
    
    <ul>
      <li v-for="product in products" :key="product.id">
        <NuxtLink :to="`/products/${product.id}`">{{ product.title }}</NuxtLink>
      </li>
    </ul>
  </div>
</template>

<script setup>
const { data: products, pending, error } = await useFetch('https://fakestoreapi.com/products')
const logout = () => {
  useCookie('token').value = null
  navigateTo('/')
}
</script>
