<template>
  <button
    class="inline-flex items-center justify-center rounded-lg text-sm font-medium px-4 h-10 bg-secondary hover:bg-secondary-200 text-foreground transition"
    :class="{
      'w-full': fullWidth,
      'h-8 px-3': small,
      'gap-2': icon
    }"
  >
    <slot />
  </button>
</template>

<script setup>
defineProps({
  fullWidth: Boolean,
  small: <PERSON>olean,
  icon: Boolean,
  label: String
})
</script>
