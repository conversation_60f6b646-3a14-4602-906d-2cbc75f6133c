{"name": "@koa/router", "description": "Router middleware for koa. Maintained by Forward Email and Lad.", "version": "12.0.2", "author": "<PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/koajs/router/issues", "email": "<EMAIL>"}, "contributors": ["<PERSON> <<EMAIL>>", "@koajs"], "dependencies": {"debug": "^4.3.4", "http-errors": "^2.0.0", "koa-compose": "^4.1.0", "methods": "^1.1.2", "path-to-regexp": "^6.3.0"}, "devDependencies": {"@commitlint/cli": "^17.7.2", "@commitlint/config-conventional": "^17.7.0", "@ladjs/env": "^4.0.0", "ava": "^5.3.1", "cross-env": "^7.0.3", "eslint": "8.39.0", "eslint-config-xo-lass": "^2.0.1", "expect.js": "^0.3.1", "fixpack": "^4.0.0", "husky": "^8.0.3", "jsdoc-to-markdown": "^8.0.0", "koa": "^2.14.2", "lint-staged": "^14.0.1", "mocha": "^10.2.0", "nyc": "^15.1.0", "remark-cli": "11", "remark-preset-github": "^4.0.4", "should": "^13.2.3", "supertest": "^6.3.3", "wrk": "^1.2.1", "xo": "0.53.1"}, "engines": {"node": ">= 12"}, "files": ["lib"], "homepage": "https://github.com/koajs/router", "keywords": ["koa", "middleware", "route", "router"], "license": "MIT", "main": "lib/router.js", "repository": {"type": "git", "url": "https://github.com/koajs/router.git"}, "scripts": {"bench": "make -C bench", "coverage": "nyc npm run test", "docs": "NODE_ENV=test jsdoc2md -t ./lib/API_tpl.hbs --src ./lib/*.js  >| API.md", "lint": "xo --fix && remark . -qfo && fixpack", "prepare": "husky install", "prextest": "npm run lint", "test": "mocha test/**/*.js", "test:watch": "mocha test/**/*.js --watch"}}