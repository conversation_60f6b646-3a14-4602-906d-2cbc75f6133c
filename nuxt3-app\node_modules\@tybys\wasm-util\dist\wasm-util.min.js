!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).wasmUtil={})}(this,(function(t){"use strict";const e="undefined"!=typeof WebAssembly?WebAssembly:"undefined"!=typeof WXWebAssembly?WXWebAssembly:void 0;if(!e)throw new Error("WebAssembly is not supported in this environment");function n(t,e){if(null===t||"object"!=typeof t)throw new TypeError(`${e} must be an object. Received ${null===t?"null":typeof t}`)}function r(t,e){if("string"!=typeof t)throw new TypeError(`${e} must be a string. Received ${null===t?"null":typeof t}`)}function i(t,e){if("function"!=typeof t)throw new TypeError(`${e} must be a function. Received ${null===t?"null":typeof t}`)}function s(t,e){if(void 0!==t)throw new TypeError(`${e} must be undefined. Received ${null===t?"null":typeof t}`)}function o(t){return!(!t||"object"!=typeof t&&"function"!=typeof t||"function"!=typeof t.then)}function a(t,e){const n=Object.create(null);return Object.keys(t).forEach((r=>{const i=t[r];Object.defineProperty(n,r,{enumerable:!0,value:e(i,r)})})),n}function c(t,e,n){return"function"==typeof SharedArrayBuffer&&t.buffer instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(t.buffer.constructor)?t.slice(e,n):t.subarray(e,n)}const u=["asyncify_get_state","asyncify_start_rewind","asyncify_start_unwind","asyncify_stop_rewind","asyncify_stop_unwind"];function f(t,e,n,r){if("function"!=typeof t.exports[r]||n<=0)return{wasm64:e,dataPtr:16,start:e?32:24,end:1024};const i=t.exports[r],s=e?Number(i(BigInt(16)+BigInt(n))):i(8+n);if(0===s)throw new Error("Allocate asyncify data failed");return e?{wasm64:e,dataPtr:s,start:s+16,end:s+16+n}:{wasm64:e,dataPtr:s,start:s+8,end:s+8+n}}class h{constructor(){this.value=void 0,this.exports=void 0,this.dataPtr=0}init(t,n,r){var i,s;if(this.exports)throw new Error("Asyncify has been initialized");if(!(t instanceof e.Memory))throw new TypeError("Require WebAssembly.Memory object");const o=n.exports;for(let t=0;t<u.length;++t)if("function"!=typeof o[u[t]])throw new TypeError("Invalid asyncify wasm");let a;const c=Boolean(r.wasm64);a=r.tryAllocate?!0===r.tryAllocate?f(n,c,4096,"malloc"):f(n,c,null!==(i=r.tryAllocate.size)&&void 0!==i?i:4096,null!==(s=r.tryAllocate.name)&&void 0!==s?s:"malloc"):{wasm64:c,dataPtr:16,start:c?32:24,end:1024},this.dataPtr=a.dataPtr,c?new BigInt64Array(t.buffer,this.dataPtr).set([BigInt(a.start),BigInt(a.end)]):new Int32Array(t.buffer,this.dataPtr).set([a.start,a.end]),this.exports=this.wrapExports(o,r.wrapExports);const h=Object.create(e.Instance.prototype);return Object.defineProperty(h,"exports",{value:this.exports}),h}assertState(){if(0!==this.exports.asyncify_get_state())throw new Error("Asyncify state error")}wrapImportFunction(t){const e=this;return function(){for(;2===e.exports.asyncify_get_state();)return e.exports.asyncify_stop_rewind(),e.value;e.assertState();const n=t.apply(this,arguments);if(!o(n))return n;e.exports.asyncify_start_unwind(e.dataPtr),e.value=n}}wrapImports(t){const e={};return Object.keys(t).forEach((n=>{const r=t[n],i={};Object.keys(r).forEach((t=>{const e=r[t];i[t]="function"==typeof e?this.wrapImportFunction(e):e})),e[n]=i})),e}wrapExportFunction(t){const e=this;return async function(){e.assertState();let n=t.apply(this,arguments);for(;1===e.exports.asyncify_get_state();)e.exports.asyncify_stop_unwind(),e.value=await e.value,e.assertState(),e.exports.asyncify_start_rewind(e.dataPtr),n=t.call(this);return e.assertState(),n}}wrapExports(t,e){return a(t,((t,n)=>{let r=-1!==u.indexOf(n)||"function"!=typeof t;return Array.isArray(e)&&(r=r||-1===e.indexOf(n)),r?t:this.wrapExportFunction(t)}))}}function d(t){if(t&&"object"!=typeof t)throw new TypeError("imports must be an object or undefined")}function g(t,n){return"undefined"!=typeof wx&&"undefined"!=typeof __wxConfig?e.instantiate(t,n):fetch(t).then((t=>t.arrayBuffer())).then((t=>e.instantiate(t,n)))}function l(t,n){let r;if(d(n),n=null!=n?n:{},t instanceof ArrayBuffer||ArrayBuffer.isView(t))return e.instantiate(t,n);if(t instanceof e.Module)return e.instantiate(t,n).then((e=>({instance:e,module:t})));if("string"!=typeof t&&!(t instanceof URL))throw new TypeError("Invalid source");if("function"==typeof e.instantiateStreaming){let i;try{i=fetch(t),r=e.instantiateStreaming(i,n).catch((()=>g(t,n)))}catch(e){r=g(t,n)}}else r=g(t,n);return r}function _(t,n){let r;if(d(n),n=null!=n?n:{},t instanceof ArrayBuffer||ArrayBuffer.isView(t))r=new e.Module(t);else{if(!(t instanceof WebAssembly.Module))throw new TypeError("Invalid source");r=t}return{instance:new e.Instance(r,n),module:r}}const y=46,p=47;function E(t){return t===p}function I(...t){let e="",n=!1;for(let i=t.length-1;i>=-1&&!n;i--){const s=i>=0?t[i]:"/";r(s,"path"),0!==s.length&&(e=`${s}/${e}`,n=s.charCodeAt(0)===p)}return e=function(t,e,n,r){let i="",s=0,o=-1,a=0,c=0;for(let u=0;u<=t.length;++u){if(u<t.length)c=t.charCodeAt(u);else{if(r(c))break;c=p}if(r(c)){if(o===u-1||1===a);else if(2===a){if(i.length<2||2!==s||i.charCodeAt(i.length-1)!==y||i.charCodeAt(i.length-2)!==y){if(i.length>2){const t=i.indexOf(n);-1===t?(i="",s=0):(i=i.slice(0,t),s=i.length-1-i.indexOf(n)),o=u,a=0;continue}if(0!==i.length){i="",s=0,o=u,a=0;continue}}e&&(i+=i.length>0?`${n}..`:"..",s=2)}else i.length>0?i+=`${n}${t.slice(o+1,u)}`:i=t.slice(o+1,u),s=u-o-1;o=u,a=0}else c===y&&-1!==a?++a:a=-1}return i}(e,!n,"/",E),n?`/${e}`:e.length>0?e:"."}const m={FD_DATASYNC:BigInt(1)<<BigInt(0),FD_READ:BigInt(1)<<BigInt(1),FD_SEEK:BigInt(1)<<BigInt(2),FD_FDSTAT_SET_FLAGS:BigInt(1)<<BigInt(3),FD_SYNC:BigInt(1)<<BigInt(4),FD_TELL:BigInt(1)<<BigInt(5),FD_WRITE:BigInt(1)<<BigInt(6),FD_ADVISE:BigInt(1)<<BigInt(7),FD_ALLOCATE:BigInt(1)<<BigInt(8),PATH_CREATE_DIRECTORY:BigInt(1)<<BigInt(9),PATH_CREATE_FILE:BigInt(1)<<BigInt(10),PATH_LINK_SOURCE:BigInt(1)<<BigInt(11),PATH_LINK_TARGET:BigInt(1)<<BigInt(12),PATH_OPEN:BigInt(1)<<BigInt(13),FD_READDIR:BigInt(1)<<BigInt(14),PATH_READLINK:BigInt(1)<<BigInt(15),PATH_RENAME_SOURCE:BigInt(1)<<BigInt(16),PATH_RENAME_TARGET:BigInt(1)<<BigInt(17),PATH_FILESTAT_GET:BigInt(1)<<BigInt(18),PATH_FILESTAT_SET_SIZE:BigInt(1)<<BigInt(19),PATH_FILESTAT_SET_TIMES:BigInt(1)<<BigInt(20),FD_FILESTAT_GET:BigInt(1)<<BigInt(21),FD_FILESTAT_SET_SIZE:BigInt(1)<<BigInt(22),FD_FILESTAT_SET_TIMES:BigInt(1)<<BigInt(23),PATH_SYMLINK:BigInt(1)<<BigInt(24),PATH_REMOVE_DIRECTORY:BigInt(1)<<BigInt(25),PATH_UNLINK_FILE:BigInt(1)<<BigInt(26),POLL_FD_READWRITE:BigInt(1)<<BigInt(27),SOCK_SHUTDOWN:BigInt(1)<<BigInt(28),SOCK_ACCEPT:BigInt(1)<<BigInt(29)};class A extends Error{constructor(t,e){super(t),this.errno=e}getErrorMessage(){return function(t){switch(t){case 0:return"Success";case 1:return"Argument list too long";case 2:return"Permission denied";case 3:return"Address in use";case 4:return"Address not available";case 5:return"Address family not supported by protocol";case 6:return"Resource temporarily unavailable";case 7:return"Operation already in progress";case 8:return"Bad file descriptor";case 9:return"Bad message";case 10:return"Resource busy";case 11:return"Operation canceled";case 12:return"No child process";case 13:return"Connection aborted";case 14:return"Connection refused";case 15:return"Connection reset by peer";case 16:return"Resource deadlock would occur";case 17:return"Destination address required";case 18:return"Domain error";case 19:return"Quota exceeded";case 20:return"File exists";case 21:return"Bad address";case 22:return"File too large";case 23:return"Host is unreachable";case 24:return"Identifier removed";case 25:return"Illegal byte sequence";case 26:return"Operation in progress";case 27:return"Interrupted system call";case 28:return"Invalid argument";case 29:return"I/O error";case 30:return"Socket is connected";case 31:return"Is a directory";case 32:return"Symbolic link loop";case 33:return"No file descriptors available";case 34:return"Too many links";case 35:return"Message too large";case 36:return"Multihop attempted";case 37:return"Filename too long";case 38:return"Network is down";case 39:return"Connection reset by network";case 40:return"Network unreachable";case 41:return"Too many files open in system";case 42:return"No buffer space available";case 43:return"No such device";case 44:return"No such file or directory";case 45:return"Exec format error";case 46:return"No locks available";case 47:return"Link has been severed";case 48:return"Out of memory";case 49:return"No message of the desired type";case 50:return"Protocol not available";case 51:return"No space left on device";case 52:return"Function not implemented";case 53:return"Socket not connected";case 54:return"Not a directory";case 55:return"Directory not empty";case 56:return"State not recoverable";case 57:return"Not a socket";case 58:return"Not supported";case 59:return"Not a tty";case 60:return"No such device or address";case 61:return"Value too large for data type";case 62:return"Previous owner died";case 63:return"Operation not permitted";case 64:return"Broken pipe";case 65:return"Protocol error";case 66:return"Protocol not supported";case 67:return"Protocol wrong type for socket";case 68:return"Result not representable";case 69:return"Read-only file system";case 70:return"Invalid seek";case 71:return"No such process";case 72:return"Stale file handle";case 73:return"Operation timed out";case 74:return"Text file busy";case 75:return"Cross-device link";case 76:return"Capabilities insufficient";default:return"Unknown error"}}(this.errno)}}Object.defineProperty(A.prototype,"name",{configurable:!0,writable:!0,value:"WasiError"});const b=m.FD_DATASYNC|m.FD_READ|m.FD_SEEK|m.FD_FDSTAT_SET_FLAGS|m.FD_SYNC|m.FD_TELL|m.FD_WRITE|m.FD_ADVISE|m.FD_ALLOCATE|m.PATH_CREATE_DIRECTORY|m.PATH_CREATE_FILE|m.PATH_LINK_SOURCE|m.PATH_LINK_TARGET|m.PATH_OPEN|m.FD_READDIR|m.PATH_READLINK|m.PATH_RENAME_SOURCE|m.PATH_RENAME_TARGET|m.PATH_FILESTAT_GET|m.PATH_FILESTAT_SET_SIZE|m.PATH_FILESTAT_SET_TIMES|m.FD_FILESTAT_GET|m.FD_FILESTAT_SET_TIMES|m.FD_FILESTAT_SET_SIZE|m.PATH_SYMLINK|m.PATH_UNLINK_FILE|m.PATH_REMOVE_DIRECTORY|m.POLL_FD_READWRITE|m.SOCK_SHUTDOWN|m.SOCK_ACCEPT,T=b,w=b,B=b,N=b,S=m.FD_DATASYNC|m.FD_READ|m.FD_SEEK|m.FD_FDSTAT_SET_FLAGS|m.FD_SYNC|m.FD_TELL|m.FD_WRITE|m.FD_ADVISE|m.FD_ALLOCATE|m.FD_FILESTAT_GET|m.FD_FILESTAT_SET_SIZE|m.FD_FILESTAT_SET_TIMES|m.POLL_FD_READWRITE,P=BigInt(0),D=m.FD_FDSTAT_SET_FLAGS|m.FD_SYNC|m.FD_ADVISE|m.PATH_CREATE_DIRECTORY|m.PATH_CREATE_FILE|m.PATH_LINK_SOURCE|m.PATH_LINK_TARGET|m.PATH_OPEN|m.FD_READDIR|m.PATH_READLINK|m.PATH_RENAME_SOURCE|m.PATH_RENAME_TARGET|m.PATH_FILESTAT_GET|m.PATH_FILESTAT_SET_SIZE|m.PATH_FILESTAT_SET_TIMES|m.FD_FILESTAT_GET|m.FD_FILESTAT_SET_TIMES|m.PATH_SYMLINK|m.PATH_UNLINK_FILE|m.PATH_REMOVE_DIRECTORY|m.POLL_FD_READWRITE,F=D|S,U=m.FD_READ|m.FD_FDSTAT_SET_FLAGS|m.FD_WRITE|m.FD_FILESTAT_GET|m.POLL_FD_READWRITE|m.SOCK_SHUTDOWN,v=b,R=m.FD_READ|m.FD_FDSTAT_SET_FLAGS|m.FD_WRITE|m.FD_FILESTAT_GET|m.POLL_FD_READWRITE,L=BigInt(0);function H(t,e,n,r){const i={base:BigInt(0),inheriting:BigInt(0)};if(0===r)throw new A("Unknown file type",28);switch(r){case 4:i.base=S,i.inheriting=P;break;case 3:i.base=D,i.inheriting=F;break;case 6:case 5:i.base=U,i.inheriting=v;break;case 2:-1!==t.indexOf(e)?(i.base=R,i.inheriting=L):(i.base=B,i.inheriting=N);break;case 1:i.base=T,i.inheriting=w;break;default:i.base=BigInt(0),i.inheriting=BigInt(0)}const s=3&n;return 0===s?i.base&=~m.FD_WRITE:1===s&&(i.base&=~m.FD_READ),i}function O(t,e){let n=0;if("number"==typeof e&&e>=0)n=e;else for(let e=0;e<t.length;e++){n+=t[e].length}let r=0;const i=new Uint8Array(n);for(let e=0;e<t.length;e++){const n=t[e];i.set(n,r),r+=n.length}return i}class k{constructor(t,e,n,r,i,s,o,a){this.id=t,this.fd=e,this.path=n,this.realPath=r,this.type=i,this.rightsBase=s,this.rightsInheriting=o,this.preopen=a,this.pos=BigInt(0),this.size=BigInt(0)}seek(t,e){if(0===e)this.pos=BigInt(t);else if(1===e)this.pos+=BigInt(t);else{if(2!==e)throw new A("Unknown whence",29);this.pos=BigInt(this.size)-BigInt(t)}return this.pos}}class C extends k{constructor(t,e,n,r,i,s,o,a,c){super(e,n,r,i,s,o,a,c),this._log=t,this._buf=null}write(t){const e=t;if(this._buf&&(t=O([this._buf,t]),this._buf=null),-1===t.indexOf(10))return this._buf=t,e.byteLength;let n,r=0,i=0;for(;-1!==(n=t.indexOf(10,r));){const e=(new TextDecoder).decode(t.subarray(i,n));this._log(e),r+=n-i+1,i=n+1}return r<t.length&&(this._buf=t.slice(r)),e.byteLength}}function x(t){return t.isBlockDevice()?1:t.isCharacterDevice()?2:t.isDirectory()?3:t.isSocket()?6:t.isFile()?4:t.isSymbolicLink()?7:0}function M(t,e,n){t.setBigUint64(e,n.dev,!0),t.setBigUint64(e+8,n.ino,!0),t.setBigUint64(e+16,BigInt(x(n)),!0),t.setBigUint64(e+24,n.nlink,!0),t.setBigUint64(e+32,n.size,!0),t.setBigUint64(e+40,n.atimeMs*BigInt(1e6),!0),t.setBigUint64(e+48,n.mtimeMs*BigInt(1e6),!0),t.setBigUint64(e+56,n.ctimeMs*BigInt(1e6),!0)}class W{constructor(t){this.used=0,this.size=t.size,this.fds=Array(t.size),this.stdio=[t.in,t.out,t.err],this.print=t.print,this.printErr=t.printErr,this.insertStdio(t.in,0,"<stdin>"),this.insertStdio(t.out,1,"<stdout>"),this.insertStdio(t.err,2,"<stderr>")}insertStdio(t,e,n){const{base:r,inheriting:i}=H(this.stdio,t,2,2),s=this.insert(t,n,n,2,r,i,0);if(s.id!==e)throw new A(`id: ${s.id} !== expected: ${e}`,8);return s}insert(t,e,n,r,i,s,o){var a,c;let u,f=-1;if(this.used>=this.size){const t=2*this.size;this.fds.length=t,f=this.size,this.size=t}else for(let t=0;t<this.size;++t)if(null==this.fds[t]){f=t;break}return u="<stdout>"===e?new C(null!==(a=this.print)&&void 0!==a?a:console.log,f,t,e,n,r,i,s,o):"<stderr>"===e?new C(null!==(c=this.printErr)&&void 0!==c?c:console.error,f,t,e,n,r,i,s,o):new k(f,t,e,n,r,i,s,o),this.fds[f]=u,this.used++,u}get(t,e,n){if(t>=this.size)throw new A("Invalid fd",8);const r=this.fds[t];if(!r||r.id!==t)throw new A("Bad file descriptor",8);if((~r.rightsBase&e)!==BigInt(0)||(~r.rightsInheriting&n)!==BigInt(0))throw new A("Capabilities insufficient",76);return r}remove(t){if(t>=this.size)throw new A("Invalid fd",8);const e=this.fds[t];if(!e||e.id!==t)throw new A("Bad file descriptor",8);this.fds[t]=void 0,this.used--}}class K extends W{constructor(t){super(t),this.fs=t.fs}getFileTypeByFd(t){return x(this.fs.fstatSync(t,{bigint:!0}))}insertPreopen(t,e,n){const r=this.getFileTypeByFd(t);if(3!==r)throw new A(`Preopen not dir: ["${e}", "${n}"]`,54);const i=H(this.stdio,t,0,r);return this.insert(t,e,n,r,i.base,i.inheriting,1)}renumber(t,e){if(t===e)return;if(t>=this.size||e>=this.size)throw new A("Invalid fd",8);const n=this.fds[t],r=this.fds[e];if(!n||!r||n.id!==t||r.id!==e)throw new A("Invalid fd",8);this.fs.closeSync(n.fd),this.fds[t]=this.fds[e],this.fds[t].id=t,this.fds[e]=void 0,this.used--}}class z extends W{constructor(t){super(t)}async getFileTypeByFd(t){return x(await t.stat({bigint:!0}))}async insertPreopen(t,e,n){const r=await this.getFileTypeByFd(t);if(3!==r)throw new A(`Preopen not dir: ["${e}", "${n}"]`,54);const i=H(this.stdio,t.fd,0,r);return this.insert(t,e,n,r,i.base,i.inheriting,1)}async renumber(t,e){if(t===e)return;if(t>=this.size||e>=this.size)throw new A("Invalid fd",8);const n=this.fds[t],r=this.fds[e];if(!n||!r||n.id!==t||r.id!==e)throw new A("Invalid fd",8);await n.fd.close(),this.fds[t]=this.fds[e],this.fds[t].id=t,this.fds[e]=void 0,this.used--}}const j=function(){return e.Memory}();class G extends j{constructor(t){super(t)}get HEAP8(){return new Int8Array(super.buffer)}get HEAPU8(){return new Uint8Array(super.buffer)}get HEAP16(){return new Int16Array(super.buffer)}get HEAPU16(){return new Uint16Array(super.buffer)}get HEAP32(){return new Int32Array(super.buffer)}get HEAPU32(){return new Uint32Array(super.buffer)}get HEAP64(){return new BigInt64Array(super.buffer)}get HEAPU64(){return new BigUint64Array(super.buffer)}get HEAPF32(){return new Float32Array(super.buffer)}get HEAPF64(){return new Float64Array(super.buffer)}get view(){return new DataView(super.buffer)}}function Y(t){return Object.getPrototypeOf(t)===e.Memory.prototype&&Object.setPrototypeOf(t,G.prototype),t}function $(){const t=e.Function;if("function"!=typeof t)throw new Error('WebAssembly.Function is not supported in this environment. If you are using V8 based browser like Chrome, try to specify --js-flags="--wasm-staging --experimental-wasm-stack-switching"');return t}function V(t,e,n){const r=$();if("function"!=typeof t)throw new TypeError("Function required");const i=e.slice(0);return i.unshift("externref"),new r({parameters:i,results:n},t,{suspending:"first"})}function Z(t){const e=$();if("function"!=typeof t)throw new TypeError("Function required");return new e({parameters:[...e.type(t).parameters.slice(1)],results:["externref"]},t,{promising:"first"})}function q(t,e){if(0===t.length||0===e.length)return 0;let n=0,r=e.length-n;for(let i=0;i<t.length;++i){const s=t[i];if(r<s.length)return s.set(e.subarray(n,n+r),0),n+=r,r=0,n;s.set(e.subarray(n,n+s.length),0),n+=s.length,r-=s.length}return n}const X=new WeakMap,Q=new WeakMap,J=new WeakMap;function tt(t){return X.get(t)}function et(t){const e=J.get(t);if(!e)throw new Error("filesystem is unavailable");return e}function nt(t){if(t instanceof A)return t.errno;switch(t.code){case"ENOENT":return 44;case"EBADF":return 8;case"EINVAL":return 28;case"EPERM":return 63;case"EPROTO":return 65;case"EEXIST":return 20;case"ENOTDIR":return 54;case"EMFILE":return 33;case"EACCES":return 2;case"EISDIR":return 31;case"ENOTEMPTY":return 55;case"ENOSYS":return 52}throw t}function rt(t,e,n){return function(t,e){return Object.defineProperty(e,"name",{value:t}),e}(e,(function(){let e;try{e=n.apply(t,arguments)}catch(t){return nt(t)}return o(e)?e.then((t=>t),nt):e}))}function it(t,e,n,r){let i=I(e.realPath,n);if(1==(1&r))try{i=t.readlinkSync(i)}catch(t){if("EINVAL"!==t.code&&"ENOENT"!==t.code)throw t}return i}async function st(t,e,n,r){let i=I(e.realPath,n);if(1==(1&r))try{i=await t.promises.readlink(i)}catch(t){if("EINVAL"!==t.code&&"ENOENT"!==t.code)throw t}return i}const ot=new TextEncoder,at=new TextDecoder,ct=(BigInt(1)<<BigInt(63))-BigInt(1);function ut(){const t=window.prompt();if(null===t)return new Uint8Array;return(new TextEncoder).encode(t+"\n")}function ft(t){return Boolean(-16&t)||3==(3&t)||12==(12&t)}class ht{constructor(t,n,r,i,s,o){this.args_get=rt(this,"args_get",(function(t,e){if(t=Number(t),e=Number(e),0===t||0===e)return 28;const{HEAPU8:n,view:r}=tt(this),i=Q.get(this).args;for(let s=0;s<i.length;++s){const o=i[s];r.setInt32(t,e,!0),t+=4;const a=ot.encode(o+"\0");n.set(a,e),e+=a.length}return 0})),this.args_sizes_get=rt(this,"args_sizes_get",(function(t,e){if(t=Number(t),e=Number(e),0===t||0===e)return 28;const{view:n}=tt(this),r=Q.get(this).args;return n.setUint32(t,r.length,!0),n.setUint32(e,ot.encode(r.join("\0")+"\0").length,!0),0})),this.environ_get=rt(this,"environ_get",(function(t,e){if(t=Number(t),e=Number(e),0===t||0===e)return 28;const{HEAPU8:n,view:r}=tt(this),i=Q.get(this).env;for(let s=0;s<i.length;++s){const o=i[s];r.setInt32(t,e,!0),t+=4;const a=ot.encode(o+"\0");n.set(a,e),e+=a.length}return 0})),this.environ_sizes_get=rt(this,"environ_sizes_get",(function(t,e){if(t=Number(t),e=Number(e),0===t||0===e)return 28;const{view:n}=tt(this),r=Q.get(this);return n.setUint32(t,r.env.length,!0),n.setUint32(e,ot.encode(r.env.join("\0")+"\0").length,!0),0})),this.clock_res_get=rt(this,"clock_res_get",(function(t,e){if(0===(e=Number(e)))return 28;const{view:n}=tt(this);switch(t){case 0:return n.setBigUint64(e,BigInt(1e6),!0),0;case 1:case 2:case 3:return n.setBigUint64(e,BigInt(1e3),!0),0;default:return 28}})),this.clock_time_get=rt(this,"clock_time_get",(function(t,e,n){if(0===(n=Number(n)))return 28;const{view:r}=tt(this);switch(t){case 0:return r.setBigUint64(n,BigInt(Date.now())*BigInt(1e6),!0),0;case 1:case 2:case 3:{const t=performance.now(),e=Math.trunc(t),i=Math.floor(1e3*(t-e)),s=BigInt(e)*BigInt(1e9)+BigInt(i)*BigInt(1e6);return r.setBigUint64(n,s,!0),0}default:return 28}})),this.fd_advise=rt(this,"fd_advise",(function(t,e,n,r){return 52})),this.fd_fdstat_get=rt(this,"fd_fdstat_get",(function(t,e){if(0===(e=Number(e)))return 28;const n=Q.get(this).fds.get(t,BigInt(0),BigInt(0)),{view:r}=tt(this);return r.setUint16(e,n.type,!0),r.setUint16(e+2,0,!0),r.setBigUint64(e+8,n.rightsBase,!0),r.setBigUint64(e+16,n.rightsInheriting,!0),0})),this.fd_fdstat_set_flags=rt(this,"fd_fdstat_set_flags",(function(t,e){return 52})),this.fd_fdstat_set_rights=rt(this,"fd_fdstat_set_rights",(function(t,e,n){const r=Q.get(this).fds.get(t,BigInt(0),BigInt(0));return(e|r.rightsBase)>r.rightsBase||(n|r.rightsInheriting)>r.rightsInheriting?76:(r.rightsBase=e,r.rightsInheriting=n,0)})),this.fd_prestat_get=rt(this,"fd_prestat_get",(function(t,e){if(0===(e=Number(e)))return 28;const n=Q.get(this);let r;try{r=n.fds.get(t,BigInt(0),BigInt(0))}catch(t){if(t instanceof A)return t.errno;throw t}if(1!==r.preopen)return 28;const{view:i}=tt(this);return i.setUint32(e,0,!0),i.setUint32(e+4,ot.encode(r.path).length,!0),0})),this.fd_prestat_dir_name=rt(this,"fd_prestat_dir_name",(function(t,e,n){if(e=Number(e),n=Number(n),0===e)return 28;const r=Q.get(this).fds.get(t,BigInt(0),BigInt(0));if(1!==r.preopen)return 8;const i=ot.encode(r.path);if(i.length>n)return 42;const{HEAPU8:s}=tt(this);return s.set(i,e),0})),this.fd_seek=rt(this,"fd_seek",(function(t,e,n,r){if(0===(r=Number(r)))return 28;if(0===t||1===t||2===t)return 0;const i=Q.get(this).fds.get(t,m.FD_SEEK,BigInt(0)).seek(e,n),{view:s}=tt(this);return s.setBigUint64(r,i,!0),0})),this.fd_tell=rt(this,"fd_tell",(function(t,e){const n=Q.get(this).fds.get(t,m.FD_TELL,BigInt(0)),r=BigInt(n.pos),{view:i}=tt(this);return i.setBigUint64(Number(e),r,!0),0})),this.poll_oneoff=rt(this,"poll_oneoff",(function(t,e,n,r){if(t=Number(t),e=Number(e),r=Number(r),n=Number(n),n>>>=0,0===t||0===e||0===n||0===r)return 28;const{view:i}=tt(this);i.setUint32(r,0,!0);let s,o=0,a=BigInt(0),c=BigInt(0),u=0,f=BigInt(0);const h=Array(n);for(o=0;o<n;o++){s=t+48*o;const e=i.getBigUint64(s,!0),n=i.getUint8(s+8),r=i.getUint32(s+16,!0),a=i.getBigUint64(s+24,!0),c=i.getBigUint64(s+32,!0),u=i.getUint16(s+40,!0);h[o]={userdata:e,type:n,u:{clock:{clock_id:r,timeout:a,precision:c,flags:u},fd_readwrite:{fd:r}}}}const d=[];for(o=0;o<n;o++)switch(s=h[o],s.type){case 0:if(1===s.u.clock.flags){const t=BigInt(Date.now())*BigInt(1e6);c=s.u.clock.timeout-t}else c=s.u.clock.timeout;(0===u||c<f)&&(f=c,a=s.userdata,u=1);break;case 1:case 2:d.push(s);break;default:return 28}if(d.length>0){for(o=0;o<d.length;o++){const t=d[o],n=e+32*o;i.setBigUint64(n,t.userdata,!0),i.setUint32(n+8,52,!0),i.setUint32(n+12,t.type,!0),i.setBigUint64(n+16,BigInt(0),!0),i.setUint16(n+24,0,!0),i.setUint32(r,1,!0)}return i.setUint32(r,d.length,!0),0}if(u){!function(t,e){const n=Date.now()+t;let r=!1;for(;Date.now()<n;)if(e()){r=!0;break}}(Number(f/BigInt(1e6)),(()=>!1));const t=e;i.setBigUint64(t,a,!0),i.setUint32(t+8,0,!0),i.setUint32(t+12,0,!0),i.setUint32(r,1,!0)}return 0})),this.proc_exit=rt(this,"proc_exit",(function(t){return"object"==typeof process&&null!==process&&"function"==typeof process.exit&&process.exit(t),0})),this.proc_raise=rt(this,"proc_raise",(function(t){return 52})),this.sched_yield=rt(this,"sched_yield",(function(){return 0})),this.random_get="undefined"!=typeof crypto&&"function"==typeof crypto.getRandomValues?rt(this,"random_get",(function(t,e){if(0===(t=Number(t)))return 28;e=Number(e);const{HEAPU8:n,view:r}=tt(this);if("function"==typeof SharedArrayBuffer&&n.buffer instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(n.buffer)){for(let n=t;n<t+e;++n)r.setUint8(n,Math.floor(256*Math.random()));return 0}let i;const s=65536;for(i=0;i+s<e;i+=s)crypto.getRandomValues(n.subarray(t+i,t+i+s));return crypto.getRandomValues(n.subarray(t+i,t+e)),0})):rt(this,"random_get",(function(t,e){if(0===(t=Number(t)))return 28;e=Number(e);const{view:n}=tt(this);for(let r=t;r<t+e;++r)n.setUint8(r,Math.floor(256*Math.random()));return 0})),this.sock_recv=rt(this,"sock_recv",(function(){return 58})),this.sock_send=rt(this,"sock_send",(function(){return 58})),this.sock_shutdown=rt(this,"sock_shutdown",(function(){return 58})),this.sock_accept=rt(this,"sock_accept",(function(){return 58})),Q.set(this,{fds:r,args:t,env:n}),s&&J.set(this,s);const a=this;function u(t,e,n,r,s){a[t]=i?o?o.wrapImportFunction(rt(a,t,n)):V(rt(a,t,n),r,s):rt(a,t,e)}function f(t,e,n,r){const i=Q.get(this).fds.get(t,m.FD_FILESTAT_SET_TIMES,BigInt(0));return 2==(2&r)&&(e=BigInt(1e6*Date.now())),8==(8&r)&&(n=BigInt(1e6*Date.now())),{fileDescriptor:i,atim:e,mtim:n}}function h(t,e,n,r){const i=(e&(m.FD_READ|m.FD_READDIR))!==BigInt(0),s=(e&(m.FD_DATASYNC|m.FD_WRITE|m.FD_ALLOCATE|m.FD_FILESTAT_SET_SIZE))!==BigInt(0);let o=s?i?2:1:0,a=m.PATH_OPEN,c=e|n;return 0!=(1&t)&&(o|=64,a|=m.PATH_CREATE_FILE),0!=(2&t)&&(o|=65536),0!=(4&t)&&(o|=128),0!=(8&t)&&(o|=512,a|=m.PATH_FILESTAT_SET_SIZE),0!=(1&r)&&(o|=1024),0!=(2&r)&&(c|=m.FD_DATASYNC),0!=(4&r)&&(o|=2048),0!=(8&r)&&(o|=1052672,c|=m.FD_SYNC),0!=(16&r)&&(o|=1052672,c|=m.FD_SYNC),s&&0==(1536&o)&&(c|=m.FD_SEEK),{flags:o,needed_base:a,needed_inheriting:c}}u("fd_allocate",(function(t,e,n){const r=Q.get(this),i=et(this),s=r.fds.get(t,m.FD_ALLOCATE,BigInt(0));return i.fstatSync(s.fd,{bigint:!0}).size<e+n&&i.ftruncateSync(s.fd,Number(e+n)),0}),(async function(t,e,n){const r=Q.get(this).fds.get(t,m.FD_ALLOCATE,BigInt(0)).fd;return(await r.stat({bigint:!0})).size<e+n&&await r.truncate(Number(e+n)),0}),["i32","i64","f64"],["i32"]),u("fd_close",(function(t){const e=Q.get(this),n=e.fds.get(t,BigInt(0),BigInt(0));return et(this).closeSync(n.fd),e.fds.remove(t),0}),(async function(t){const e=Q.get(this),n=e.fds.get(t,BigInt(0),BigInt(0));return await n.fd.close(),e.fds.remove(t),0}),["i32"],["i32"]),u("fd_datasync",(function(t){const e=Q.get(this).fds.get(t,m.FD_DATASYNC,BigInt(0));return et(this).fdatasyncSync(e.fd),0}),(async function(t){const e=Q.get(this).fds.get(t,m.FD_DATASYNC,BigInt(0));return await e.fd.datasync(),0}),["i32"],["i32"]),u("fd_filestat_get",(function(t,e){if(0===(e=Number(e)))return 28;const n=Q.get(this).fds.get(t,m.FD_FILESTAT_GET,BigInt(0)),r=et(this).fstatSync(n.fd,{bigint:!0}),{view:i}=tt(this);return M(i,e,r),0}),(async function(t,e){if(0===(e=Number(e)))return 28;const n=Q.get(this).fds.get(t,m.FD_FILESTAT_GET,BigInt(0)).fd,r=await n.stat({bigint:!0}),{view:i}=tt(this);return M(i,e,r),0}),["i32","i32"],["i32"]),u("fd_filestat_set_size",(function(t,e){const n=Q.get(this).fds.get(t,m.FD_FILESTAT_SET_SIZE,BigInt(0));return et(this).ftruncateSync(n.fd,Number(e)),0}),(async function(t,e){const n=Q.get(this).fds.get(t,m.FD_FILESTAT_SET_SIZE,BigInt(0)).fd;return await n.truncate(Number(e)),0}),["i32","i64"],["i32"]),u("fd_filestat_set_times",(function(t,e,n,r){if(ft(r))return 28;const{fileDescriptor:i,atim:s,mtim:o}=f.call(this,t,e,n,r);return et(this).futimesSync(i.fd,Number(s),Number(o)),0}),(async function(t,e,n,r){if(ft(r))return 28;const{fileDescriptor:i,atim:s,mtim:o}=f.call(this,t,e,n,r),a=i.fd;return await a.utimes(Number(s),Number(o)),0}),["i32","i64","i64","i32"],["i32"]),u("fd_pread",(function(t,e,n,r,i){if(e=Number(e),i=Number(i),0===e&&n||0===i||r>ct)return 28;const{HEAPU8:s,view:o}=tt(this),a=Q.get(this).fds.get(t,m.FD_READ|m.FD_SEEK,BigInt(0));if(!n)return o.setUint32(i,0,!0),0;let c=0;const u=Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,i=o.getInt32(r,!0),a=o.getUint32(r+4,!0);return c+=a,s.subarray(i,i+a)}));let f=0;const h=(()=>{try{return new Uint8Array(new SharedArrayBuffer(c))}catch(t){return new Uint8Array(c)}})();h._isBuffer=!0;const d=et(this).readSync(a.fd,h,0,h.length,Number(r));return f=h?q(u,h.subarray(0,d)):0,o.setUint32(i,f,!0),0}),(async function(t,e,n,r,i){if(e=Number(e),i=Number(i),0===e&&n||0===i||r>ct)return 28;const{HEAPU8:s,view:o}=tt(this),a=Q.get(this).fds.get(t,m.FD_READ|m.FD_SEEK,BigInt(0));if(!n)return o.setUint32(i,0,!0),0;let c=0;const u=Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,i=o.getInt32(r,!0),a=o.getUint32(r+4,!0);return c+=a,s.subarray(i,i+a)}));let f=0;const h=new Uint8Array(c);h._isBuffer=!0;const{bytesRead:d}=await a.fd.read(h,0,h.length,Number(r));return f=h?q(u,h.subarray(0,d)):0,o.setUint32(i,f,!0),0}),["i32","i32","i32","i64","i32"],["i32"]),u("fd_pwrite",(function(t,e,n,r,i){if(e=Number(e),i=Number(i),0===e&&n||0===i||r>ct)return 28;const{HEAPU8:s,view:o}=tt(this),a=Q.get(this).fds.get(t,m.FD_WRITE|m.FD_SEEK,BigInt(0));if(!n)return o.setUint32(i,0,!0),0;const c=O(Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,i=o.getInt32(r,!0),a=o.getUint32(r+4,!0);return s.subarray(i,i+a)}))),u=et(this).writeSync(a.fd,c,0,c.length,Number(r));return o.setUint32(i,u,!0),0}),(async function(t,e,n,r,i){if(e=Number(e),i=Number(i),0===e&&n||0===i||r>ct)return 28;const{HEAPU8:s,view:o}=tt(this),a=Q.get(this).fds.get(t,m.FD_WRITE|m.FD_SEEK,BigInt(0));if(!n)return o.setUint32(i,0,!0),0;const c=O(Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,i=o.getInt32(r,!0),a=o.getUint32(r+4,!0);return s.subarray(i,i+a)}))),{bytesWritten:u}=await a.fd.write(c,0,c.length,Number(r));return o.setUint32(i,u,!0),0}),["i32","i32","i32","i64","i32"],["i32"]),u("fd_read",(function(t,e,n,r){if(e=Number(e),r=Number(r),0===e&&n||0===r)return 28;const{HEAPU8:i,view:s}=tt(this),o=Q.get(this).fds.get(t,m.FD_READ,BigInt(0));if(!n)return s.setUint32(r,0,!0),0;let a=0;const c=Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,o=s.getInt32(r,!0),c=s.getUint32(r+4,!0);return a+=c,i.subarray(o,o+c)}));let u,f=0;if(0===t){if("undefined"==typeof window||"function"!=typeof window.prompt)return 58;u=ut(),f=u?q(c,u):0}else{u=(()=>{try{return new Uint8Array(new SharedArrayBuffer(a))}catch(t){return new Uint8Array(a)}})(),u._isBuffer=!0;const t=et(this).readSync(o.fd,u,0,u.length,Number(o.pos));f=u?q(c,u.subarray(0,t)):0,o.pos+=BigInt(f)}return s.setUint32(r,f,!0),0}),(async function(t,e,n,r){if(e=Number(e),r=Number(r),0===e&&n||0===r)return 28;const{HEAPU8:i,view:s}=tt(this),o=Q.get(this).fds.get(t,m.FD_READ,BigInt(0));if(!n)return s.setUint32(r,0,!0),0;let a=0;const c=Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,o=s.getInt32(r,!0),c=s.getUint32(r+4,!0);return a+=c,i.subarray(o,o+c)}));let u,f=0;if(0===t){if("undefined"==typeof window||"function"!=typeof window.prompt)return 58;u=ut(),f=u?q(c,u):0}else{u=new Uint8Array(a),u._isBuffer=!0;const{bytesRead:t}=await o.fd.read(u,0,u.length,Number(o.pos));f=u?q(c,u.subarray(0,t)):0,o.pos+=BigInt(f)}return s.setUint32(r,f,!0),0}),["i32","i32","i32","i32"],["i32"]),u("fd_readdir",(function(t,e,n,r,i){if(e=Number(e),n=Number(n),i=Number(i),0===e||0===i)return 0;const s=Q.get(this).fds.get(t,m.FD_READDIR,BigInt(0)),o=et(this),a=o.readdirSync(s.realPath,{withFileTypes:!0}),{HEAPU8:c,view:u}=tt(this);let f=0;for(let t=Number(r);t<a.length;t++){const r=ot.encode(a[t].name),i=o.statSync(I(s.realPath,a[t].name),{bigint:!0}),u=new Uint8Array(24+r.byteLength),h=new DataView(u.buffer);let d;h.setBigUint64(0,BigInt(t+1),!0),h.setBigUint64(8,BigInt(i.ino?i.ino:0),!0),h.setUint32(16,r.byteLength,!0),d=a[t].isFile()?4:a[t].isDirectory()?3:a[t].isSymbolicLink()?7:a[t].isCharacterDevice()?2:a[t].isBlockDevice()?1:a[t].isSocket()?6:0,h.setUint8(20,d),u.set(r,24);const g=u.slice(0,Math.min(u.length,n-f));c.set(g,e+f),f+=g.byteLength}return u.setUint32(i,f,!0),0}),(async function(t,e,n,r,i){if(e=Number(e),n=Number(n),i=Number(i),0===e||0===i)return 0;const s=Q.get(this).fds.get(t,m.FD_READDIR,BigInt(0)),o=et(this),a=await o.promises.readdir(s.realPath,{withFileTypes:!0}),{HEAPU8:c,view:u}=tt(this);let f=0;for(let t=Number(r);t<a.length;t++){const r=ot.encode(a[t].name),i=await o.promises.stat(I(s.realPath,a[t].name),{bigint:!0}),u=new Uint8Array(24+r.byteLength),h=new DataView(u.buffer);let d;h.setBigUint64(0,BigInt(t+1),!0),h.setBigUint64(8,BigInt(i.ino?i.ino:0),!0),h.setUint32(16,r.byteLength,!0),d=a[t].isFile()?4:a[t].isDirectory()?3:a[t].isSymbolicLink()?7:a[t].isCharacterDevice()?2:a[t].isBlockDevice()?1:a[t].isSocket()?6:0,h.setUint8(20,d),u.set(r,24);const g=u.slice(0,Math.min(u.length,n-f));c.set(g,e+f),f+=g.byteLength}return u.setUint32(i,f,!0),0}),["i32","i32","i32","i64","i32"],["i32"]),u("fd_renumber",(function(t,e){return Q.get(this).fds.renumber(e,t),0}),(async function(t,e){const n=Q.get(this);return await n.fds.renumber(e,t),0}),["i32","i32"],["i32"]),u("fd_sync",(function(t){const e=Q.get(this).fds.get(t,m.FD_SYNC,BigInt(0));return et(this).fsyncSync(e.fd),0}),(async function(t){const e=Q.get(this).fds.get(t,m.FD_SYNC,BigInt(0));return await e.fd.sync(),0}),["i32"],["i32"]),u("fd_write",(function(t,e,n,r){if(e=Number(e),r=Number(r),0===e&&n||0===r)return 28;const{HEAPU8:i,view:s}=tt(this),o=Q.get(this).fds.get(t,m.FD_WRITE,BigInt(0));if(!n)return s.setUint32(r,0,!0),0;const a=O(Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,o=s.getInt32(r,!0),a=s.getUint32(r+4,!0);return i.subarray(o,o+a)})));let c;if(1===t||2===t)c=o.write(a);else{c=et(this).writeSync(o.fd,a,0,a.length,Number(o.pos)),o.pos+=BigInt(c)}return s.setUint32(r,c,!0),0}),(async function(t,e,n,r){if(e=Number(e),r=Number(r),0===e&&n||0===r)return 28;const{HEAPU8:i,view:s}=tt(this),o=Q.get(this).fds.get(t,m.FD_WRITE,BigInt(0));if(!n)return s.setUint32(r,0,!0),0;const a=O(Array.from({length:Number(n)},((t,n)=>{const r=e+8*n,o=s.getInt32(r,!0),a=s.getUint32(r+4,!0);return i.subarray(o,o+a)})));let c;return 1===t||2===t?c=o.write(a):(c=await(await o.fd.write(a,0,a.length,Number(o.pos))).bytesWritten,o.pos+=BigInt(c)),s.setUint32(r,c,!0),0}),["i32","i32","i32","i32"],["i32"]),u("path_create_directory",(function(t,e,n){if(e=Number(e),n=Number(n),0===e)return 28;const{HEAPU8:r}=tt(this),i=Q.get(this).fds.get(t,m.PATH_CREATE_DIRECTORY,BigInt(0));let s=at.decode(c(r,e,e+n));s=I(i.realPath,s);return et(this).mkdirSync(s),0}),(async function(t,e,n){if(e=Number(e),n=Number(n),0===e)return 28;const{HEAPU8:r}=tt(this),i=Q.get(this).fds.get(t,m.PATH_CREATE_DIRECTORY,BigInt(0));let s=at.decode(c(r,e,e+n));s=I(i.realPath,s);const o=et(this);return await o.promises.mkdir(s),0}),["i32","i32","i32"],["i32"]),u("path_filestat_get",(function(t,e,n,r,i){if(n=Number(n),r=Number(r),i=Number(i),0===n||0===i)return 28;const{HEAPU8:s,view:o}=tt(this),a=Q.get(this).fds.get(t,m.PATH_FILESTAT_GET,BigInt(0));let u=at.decode(c(s,n,n+r));const f=et(this);let h;return u=I(a.realPath,u),h=1==(1&e)?f.statSync(u,{bigint:!0}):f.lstatSync(u,{bigint:!0}),M(o,i,h),0}),(async function(t,e,n,r,i){if(n=Number(n),r=Number(r),i=Number(i),0===n||0===i)return 28;const{HEAPU8:s,view:o}=tt(this),a=Q.get(this).fds.get(t,m.PATH_FILESTAT_GET,BigInt(0));let u=at.decode(c(s,n,n+r));const f=et(this);let h;return u=I(a.realPath,u),h=1==(1&e)?await f.promises.stat(u,{bigint:!0}):await f.promises.lstat(u,{bigint:!0}),M(o,i,h),0}),["i32","i32","i32","i32","i32"],["i32"]),u("path_filestat_set_times",(function(t,e,n,r,i,s,o){if(n=Number(n),r=Number(r),0===n)return 28;const{HEAPU8:a}=tt(this),u=Q.get(this).fds.get(t,m.PATH_FILESTAT_SET_TIMES,BigInt(0));if(ft(o))return 28;const f=et(this),h=it(f,u,at.decode(c(a,n,n+r)),e);return 2==(2&o)&&(i=BigInt(1e6*Date.now())),8==(8&o)&&(s=BigInt(1e6*Date.now())),f.utimesSync(h,Number(i),Number(s)),0}),(async function(t,e,n,r,i,s,o){if(n=Number(n),r=Number(r),0===n)return 28;const{HEAPU8:a}=tt(this),u=Q.get(this).fds.get(t,m.PATH_FILESTAT_SET_TIMES,BigInt(0));if(ft(o))return 28;const f=et(this),h=await st(f,u,at.decode(c(a,n,n+r)),e);return 2==(2&o)&&(i=BigInt(1e6*Date.now())),8==(8&o)&&(s=BigInt(1e6*Date.now())),await f.promises.utimes(h,Number(i),Number(s)),0}),["i32","i32","i32","i32","i64","i64","i32"],["i32"]),u("path_link",(function(t,e,n,r,i,s,o){if(n=Number(n),r=Number(r),s=Number(s),o=Number(o),0===n||0===s)return 28;const a=Q.get(this);let u,f;t===i?u=f=a.fds.get(t,m.PATH_LINK_SOURCE|m.PATH_LINK_TARGET,BigInt(0)):(u=a.fds.get(t,m.PATH_LINK_SOURCE,BigInt(0)),f=a.fds.get(i,m.PATH_LINK_TARGET,BigInt(0)));const{HEAPU8:h}=tt(this),d=et(this),g=it(d,u,at.decode(c(h,n,n+r)),e),l=I(f.realPath,at.decode(c(h,s,s+o)));return d.linkSync(g,l),0}),(async function(t,e,n,r,i,s,o){if(n=Number(n),r=Number(r),s=Number(s),o=Number(o),0===n||0===s)return 28;const a=Q.get(this);let u,f;t===i?u=f=a.fds.get(t,m.PATH_LINK_SOURCE|m.PATH_LINK_TARGET,BigInt(0)):(u=a.fds.get(t,m.PATH_LINK_SOURCE,BigInt(0)),f=a.fds.get(i,m.PATH_LINK_TARGET,BigInt(0)));const{HEAPU8:h}=tt(this),d=et(this),g=await st(d,u,at.decode(c(h,n,n+r)),e),l=I(f.realPath,at.decode(c(h,s,s+o)));return await d.promises.link(g,l),0}),["i32","i32","i32","i32","i32","i32","i32"],["i32"]),u("path_open",(function(t,e,n,r,i,s,o,a,u){if(n=Number(n),u=Number(u),0===n||0===u)return 28;r=Number(r),s=BigInt(s),o=BigInt(o);const{flags:f,needed_base:d,needed_inheriting:g}=h(i,s,o,a),l=Q.get(this),_=l.fds.get(t,d,g),y=tt(this),p=y.HEAPU8,E=at.decode(c(p,n,n+r)),I=et(this),m=it(I,_,E,e),A=I.openSync(m,f,438),b=l.fds.getFileTypeByFd(A);if(0!=(2&i)&&3!==b)return 54;const{base:T,inheriting:w}=H(l.fds.stdio,A,f,b),B=l.fds.insert(A,m,m,b,s&T,o&w,0),N=I.fstatSync(A,{bigint:!0});N.isFile()&&(B.size=N.size,0!=(1024&f)&&(B.pos=N.size));return y.view.setInt32(u,B.id,!0),0}),(async function(t,e,n,r,i,s,o,a,u){if(n=Number(n),u=Number(u),0===n||0===u)return 28;r=Number(r),s=BigInt(s),o=BigInt(o);const{flags:f,needed_base:d,needed_inheriting:g}=h(i,s,o,a),l=Q.get(this),_=l.fds.get(t,d,g),y=tt(this),p=y.HEAPU8,E=at.decode(c(p,n,n+r)),I=et(this),m=await st(I,_,E,e),A=await I.promises.open(m,f,438),b=await l.fds.getFileTypeByFd(A);if(0!=(2&i)&&3!==b)return 54;const{base:T,inheriting:w}=H(l.fds.stdio,A.fd,f,b),B=l.fds.insert(A,m,m,b,s&T,o&w,0),N=await A.stat({bigint:!0});N.isFile()&&(B.size=N.size,0!=(1024&f)&&(B.pos=N.size));return y.view.setInt32(u,B.id,!0),0}),["i32","i32","i32","i32","i32","i64","i64","i32","i32"],["i32"]),u("path_readlink",(function(t,e,n,r,i,s){if(e=Number(e),n=Number(n),r=Number(r),i=Number(i),s=Number(s),0===e||0===r||0===s)return 28;const{HEAPU8:o,view:a}=tt(this),u=Q.get(this).fds.get(t,m.PATH_READLINK,BigInt(0));let f=at.decode(c(o,e,e+n));f=I(u.realPath,f);const h=et(this).readlinkSync(f),d=ot.encode(h),g=Math.min(d.length,i);return g>=i?42:(o.set(d.subarray(0,g),r),o[r+g]=0,a.setUint32(s,g,!0),0)}),(async function(t,e,n,r,i,s){if(e=Number(e),n=Number(n),r=Number(r),i=Number(i),s=Number(s),0===e||0===r||0===s)return 28;const{HEAPU8:o,view:a}=tt(this),u=Q.get(this).fds.get(t,m.PATH_READLINK,BigInt(0));let f=at.decode(c(o,e,e+n));f=I(u.realPath,f);const h=et(this),d=await h.promises.readlink(f),g=ot.encode(d),l=Math.min(g.length,i);return l>=i?42:(o.set(g.subarray(0,l),r),o[r+l]=0,a.setUint32(s,l,!0),0)}),["i32","i32","i32","i32","i32","i32"],["i32"]),u("path_remove_directory",(function(t,e,n){if(e=Number(e),n=Number(n),0===e)return 28;const{HEAPU8:r}=tt(this),i=Q.get(this).fds.get(t,m.PATH_REMOVE_DIRECTORY,BigInt(0));let s=at.decode(c(r,e,e+n));s=I(i.realPath,s);return et(this).rmdirSync(s),0}),(async function(t,e,n){if(e=Number(e),n=Number(n),0===e)return 28;const{HEAPU8:r}=tt(this),i=Q.get(this).fds.get(t,m.PATH_REMOVE_DIRECTORY,BigInt(0));let s=at.decode(c(r,e,e+n));s=I(i.realPath,s);const o=et(this);return await o.promises.rmdir(s),0}),["i32","i32","i32"],["i32"]),u("path_rename",(function(t,e,n,r,i,s){if(e=Number(e),n=Number(n),i=Number(i),s=Number(s),0===e||0===i)return 28;const o=Q.get(this);let a,u;t===r?a=u=o.fds.get(t,m.PATH_RENAME_SOURCE|m.PATH_RENAME_TARGET,BigInt(0)):(a=o.fds.get(t,m.PATH_RENAME_SOURCE,BigInt(0)),u=o.fds.get(r,m.PATH_RENAME_TARGET,BigInt(0)));const{HEAPU8:f}=tt(this),h=I(a.realPath,at.decode(c(f,e,e+n))),d=I(u.realPath,at.decode(c(f,i,i+s)));return et(this).renameSync(h,d),0}),(async function(t,e,n,r,i,s){if(e=Number(e),n=Number(n),i=Number(i),s=Number(s),0===e||0===i)return 28;const o=Q.get(this);let a,u;t===r?a=u=o.fds.get(t,m.PATH_RENAME_SOURCE|m.PATH_RENAME_TARGET,BigInt(0)):(a=o.fds.get(t,m.PATH_RENAME_SOURCE,BigInt(0)),u=o.fds.get(r,m.PATH_RENAME_TARGET,BigInt(0)));const{HEAPU8:f}=tt(this),h=I(a.realPath,at.decode(c(f,e,e+n))),d=I(u.realPath,at.decode(c(f,i,i+s))),g=et(this);return await g.promises.rename(h,d),0}),["i32","i32","i32","i32","i32","i32"],["i32"]),u("path_symlink",(function(t,e,n,r,i){if(t=Number(t),e=Number(e),r=Number(r),i=Number(i),0===t||0===r)return 28;const{HEAPU8:s}=tt(this),o=Q.get(this).fds.get(n,m.PATH_SYMLINK,BigInt(0)),a=at.decode(c(s,t,t+e));let u=at.decode(c(s,r,r+i));u=I(o.realPath,u);return et(this).symlinkSync(a,u),0}),(async function(t,e,n,r,i){if(t=Number(t),e=Number(e),r=Number(r),i=Number(i),0===t||0===r)return 28;const{HEAPU8:s}=tt(this),o=Q.get(this).fds.get(n,m.PATH_SYMLINK,BigInt(0)),a=at.decode(c(s,t,t+e));let u=at.decode(c(s,r,r+i));u=I(o.realPath,u);const f=et(this);return await f.promises.symlink(a,u),0}),["i32","i32","i32","i32","i32"],["i32"]),u("path_unlink_file",(function(t,e,n){if(e=Number(e),n=Number(n),0===e)return 28;const{HEAPU8:r}=tt(this),i=Q.get(this).fds.get(t,m.PATH_UNLINK_FILE,BigInt(0));let s=at.decode(c(r,e,e+n));s=I(i.realPath,s);return et(this).unlinkSync(s),0}),(async function(t,e,n){if(e=Number(e),n=Number(n),0===e)return 28;const{HEAPU8:r}=tt(this),i=Q.get(this).fds.get(t,m.PATH_UNLINK_FILE,BigInt(0));let s=at.decode(c(r,e,e+n));s=I(i.realPath,s);const o=et(this);return await o.promises.unlink(s),0}),["i32","i32","i32"],["i32"]),this._setMemory=function(t){if(!(t instanceof e.Memory))throw new TypeError('"instance.exports.memory" property must be a WebAssembly.Memory');X.set(a,Y(t))}}static createSync(t,e,n,r,i,s,o){const a=new K({size:3,in:r[0],out:r[1],err:r[2],fs:i,print:s,printErr:o}),c=new ht(t,e,a,!1,i);if(n.length>0)for(let t=0;t<n.length;++t){const e=i.realpathSync(n[t].realPath,"utf8"),r=i.openSync(e,"r",438);a.insertPreopen(r,n[t].mappedPath,e)}return c}static async createAsync(t,e,n,r,i,s,o,a){const c=new z({size:3,in:r[0],out:r[1],err:r[2],print:s,printErr:o}),u=new ht(t,e,c,!0,i,a);if(n.length>0)for(let t=0;t<n.length;++t){const e=n[t],r=await i.promises.realpath(e.realPath),s=await i.promises.open(r,"r",438);await c.insertPreopen(s,e.mappedPath,r)}return u}}const dt=Object.freeze(Object.create(null)),gt=Symbol("kExitCode"),lt=Symbol("kSetMemory"),_t=Symbol("kStarted"),yt=Symbol("kInstance"),pt=Symbol("kBindingName");function Et(t,e){n(e,"instance"),n(e.exports,"instance.exports"),t[yt]=e,t[lt](e.exports.memory)}function It(t){var e;let s;if(n(t,"options"),void 0!==t.version)switch(r(t.version,"options.version"),t.version){case"unstable":s=ht,this[pt]="wasi_unstable";break;case"preview1":s=ht,this[pt]="wasi_snapshot_preview1";break;default:throw new TypeError(`unsupported WASI version "${t.version}"`)}else s=ht,this[pt]="wasi_snapshot_preview1";void 0!==t.args&&function(t,e){if(!Array.isArray(t))throw new TypeError(`${e} must be an array. Received ${null===t?"null":typeof t}`)}(t.args,"options.args");const o=(null!==(e=t.args)&&void 0!==e?e:[]).map(String),a=[];void 0!==t.env&&(n(t.env,"options.env"),Object.entries(t.env).forEach((({0:t,1:e})=>{void 0!==e&&a.push(`${t}=${e}`)})));const c=[];if(void 0!==t.preopens&&(n(t.preopens,"options.preopens"),Object.entries(t.preopens).forEach((({0:t,1:e})=>c.push({mappedPath:String(t),realPath:String(e)})))),c.length>0){if(void 0===t.fs)throw new Error("filesystem is disabled, can not preopen directory");try{n(t.fs,"options.fs")}catch(t){throw new TypeError("Node.js fs like implementation is not provided")}}void 0!==t.print&&i(t.print,"options.print"),void 0!==t.printErr&&i(t.printErr,"options.printErr"),void 0!==t.returnOnExit&&function(t,e){if("boolean"!=typeof t)throw new TypeError(`${e} must be a boolean. Received ${null===t?"null":typeof t}`)}(t.returnOnExit,"options.returnOnExit");return{args:o,env:a,preopens:c,stdio:[0,1,2],_WASI:s}}function mt(t,e){this[lt]=t,this.wasiImport=e,this[_t]=!1,this[gt]=0,this[yt]=void 0}class At{constructor(t=dt){const{args:e,env:n,preopens:r,stdio:i,_WASI:s}=It.call(this,t),o=s.createSync(e,n,r,i,t.fs,t.print,t.printErr),a=o._setMemory;delete o._setMemory,mt.call(this,a,o),t.returnOnExit&&(o.proc_exit=bt.bind(this))}start(t){if(this[_t])throw new Error("WASI instance has already started");this[_t]=!0,Et(this,t);const{_start:e,_initialize:n}=this[yt].exports;let r;i(e,"instance.exports._start"),s(n,"instance.exports._initialize");try{r=e()}catch(t){if(t!==gt)throw t}return r instanceof Promise?r.then((()=>this[gt]),(t=>{if(t!==gt)throw t;return this[gt]})):this[gt]}initialize(t){if(this[_t])throw new Error("WASI instance has already started");this[_t]=!0,Et(this,t);const{_start:e,_initialize:n}=this[yt].exports;if(s(e,"instance.exports._start"),void 0!==n)return i(n,"instance.exports._initialize"),n()}getImportObject(){return{[this[pt]]:this.wasiImport}}}function bt(t){throw this[gt]=t,gt}t.Asyncify=h,t.Memory=G,t.WASI=At,t.WebAssemblyMemory=j,t.asyncifyLoad=function(t,e,n){d(n),n=null!=n?n:{};const r=new h;return l(e,n=r.wrapImports(n)).then((e=>{var i;const s=e.instance.exports.memory||(null===(i=n.env)||void 0===i?void 0:i.memory);return{module:e.module,instance:r.init(s,e.instance,t)}}))},t.asyncifyLoadSync=function(t,e,n){var r;d(n),n=null!=n?n:{};const i=new h,s=_(e,n=i.wrapImports(n)),o=s.instance.exports.memory||(null===(r=n.env)||void 0===r?void 0:r.memory);return{module:s.module,instance:i.init(o,s.instance,t)}},t.createAsyncWASI=async function(t=dt){const e=Object.create(At.prototype),{args:r,env:s,preopens:o,stdio:a,_WASI:c}=It.call(e,t);void 0!==t.asyncify&&(n(t.asyncify,"options.asyncify"),i(t.asyncify.wrapImportFunction,"options.asyncify.wrapImportFunction"));const u=await c.createAsync(r,s,o,a,t.fs,t.print,t.printErr,t.asyncify),f=u._setMemory;return delete u._setMemory,mt.call(e,f,u),t.returnOnExit&&(u.proc_exit=bt.bind(e)),e},t.extendMemory=Y,t.load=l,t.loadSync=_,t.wrapAsyncExport=Z,t.wrapAsyncImport=V,t.wrapExports=function(t,e){return a(t,((t,n)=>{let r="function"!=typeof t;return Array.isArray(e)&&(r=r||-1===e.indexOf(n)),r?t:Z(t)}))},Object.defineProperty(t,"__esModule",{value:!0})}));
