export const useAuthStore = defineStore('auth', () => {
  const token = useCookie<string | null>('token', { default: () => null })

  const login = async (username: string, password: string) => {
    try {
      const res = await $fetch('https://fakestoreapi.com/auth/login', {
        method: 'POST',
        body: { username, password },
      })
      token.value = res.token
      return true
    } catch (err) {
      return err
    }
  }

  const logout = () => {
    token.value = null
  }

  return { token, login, logout }
})
