<template>
  <header class="flex items-center justify-between border-b border-border px-4 md:px-10 py-3 bg-white dark:bg-card">
    <div class="flex items-center gap-4">
      <LogoIcon />
      <h1 class="text-lg font-bold tracking-tight">Bookify</h1>
    </div>
    <nav class="hidden md:flex gap-6 text-sm font-medium">
      <NavLink to="#">Dashboard</NavLink>
      <NavLink to="#">Bookings</NavLink>
      <NavLink to="#">Customers</NavLink>
      <NavLink to="#">Services</NavLink>
      <NavLink to="#">Staff</NavLink>
    </nav>
    <div class="flex items-center gap-4">
      <BaseButton icon>
        <BellIcon />
      </BaseButton>
      <UserAvatar :src="userImage" />
    </div>
  </header>
</template>

<script setup>
// import LogoIcon from './icons/LogoIcon.vue'
// import BellIcon from './icons/BellIcon.vue'
import NavLink from './NavLink.vue'
import BaseButton from './BaseButton.vue'
import UserAvatar from './UserAvatar.vue'

const userImage = 'https://lh3.googleusercontent.com/aida-public/...'
</script>
